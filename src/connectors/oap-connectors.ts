import axios from "axios";

export interface SaveStudentDetailsPayload {
    [key: string]: any;
}

export interface SaveStudentDetailsResponse {
    applicationId: string;
    [key: string]: any;
}

export const postOAPData = async (
    path,
    payload: SaveStudentDetailsPayload,
    key
): Promise<SaveStudentDetailsResponse> => {
    try {
        const url = `${process.env.OAP_API}/${path}`;
        const headers = {
            "x-api-key": key,
            "Content-Type": "application/json",
        };
        console.log(url, headers, payload)
        const response = await axios.post(url, payload, { headers });
        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
            throw error.response.data;
        }
        throw error;
    }
};

export const getData: any = async (endpoint, APIKEY, queryParams = {}) => {
    try {
        const path = `${process.env.OAP_API}/${endpoint}`;
        let url
        const queryString = new URLSearchParams(queryParams)?.toString();
        url = queryString ? `${path}?${queryString}` : path;
        // console.log("URL -->", url);
        const headers = {
            "x-api-key": APIKEY || process.env.UCW_KEY,
        };
        // console.log("headers -->", headers);
        const response = await axios.get(url, { headers });
        // console.log("response -->", response);
        return response?.data;
    } catch (error) {
        console.log("Error ", error.response);
        throw error?.response?.data;
    }
};
