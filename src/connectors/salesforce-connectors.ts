import axios from "axios";
export const postData: any = async (endpoint, requestData: any, APIKEY) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
    console.log("path -->", path)
    const headers = {
      "x-api-key": APIKEY ? APIKEY : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    console.log("headers -->", headers)
    const response = await axios.post(path, requestData, { headers });
    console.log("response -->", response)
    console.log("response data -->", response.data)
    return response.data;
  } catch (error) {
    console.log("Error ", error.response)
    throw error?.response?.data;
  }
};

export const postDataSf: any = async (requestData: any, APIKEY, applicationFilledBy, reprocess = false) => {
  try {
    if (applicationFilledBy === 'agent') {
      return await postData("gus/persistapplication", requestData, APIKEY);
    } else if (applicationFilledBy === 'student' && reprocess === false) {
      return await postData("gus/persiststudentapplication", requestData, APIKEY);
    } else if(applicationFilledBy === 'student' && reprocess === true){
      return await postData("gus/persistapplicationreprocess", requestData, APIKEY);
    }
  } catch (error) {
    throw error
  }
};

export const getEipData = async (endpoint, correlationId?: string, apiKey?) => {
  try {
    const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
    const headers = {
      "x-api-key": apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
    };
    console.log('path', path)
    console.log('headers', headers)
    if (correlationId) {
      headers["Correlation-Id"] = correlationId;
    }
    const response = await axios.get(path, {
      headers: headers,
    });
    
    return await response.data;
  } catch (error) {
    console.log("Err", error);
    throw error?.response?.data;
  }
};