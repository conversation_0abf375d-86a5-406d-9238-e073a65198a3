import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "src/functions/ucw-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
import { DynamoDBService } from "src/common/dynamodbService";
const dbService = new DynamoDBService();
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleUcwSfSaveOrUpdateRequests = async (event) => {
  console.log(JSON.stringify(event));
  const brand = "UCW";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    console.log("App details -->", applicationDetails);
    const isReprocess = applicationDetails?.reprocess;
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let response;
    request["sectionLabel"] = applicationDetails.sectionLabel;
    request["email"] = applicationDetails.email;
    request["applicationId"] = applicationDetails.applicationId;
    request["requestId"] = requestId;
    request["messageId"] = record.messageId;
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
    }
    let referenceMappedDetails;
    console.log(
      "Modified Application details document -->",
      applicationDetails.documents
    );
    try {
      if (
        !applicationDetails?.isPermanentAddressDiffer ||
        applicationDetails?.isPermanentAddressDiffer === "No"
      ) {
        applicationDetails = {
          ...applicationDetails,
          permanentStreetAddress: applicationDetails?.streetAddress,
          permanentCity: applicationDetails?.city,
          permanentPostalCode: applicationDetails?.postalCode,
          permanentCountry: applicationDetails?.country,
          permanentCountryDisplayName: applicationDetails?.countryDisplayName,
        };
      }
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.hasActiveLeadOwner &&
        filledBy === "student"
      ) {
        const draftApplicationDetails = await formDraftApplicationDetails(
          applicationDetails
        );
        const draftRequest = {
          ...request,
          ...(await mapSalesforceObject(draftApplicationDetails, filledBy)),
        };
        response = await postDataSf(
          draftRequest,
          APIKEY,
          filledBy,
          isReprocess === true ? true : false
        );
        await dbService.updateObject(
          `gus-oap-student-applications-${process.env.STAGE}`,
          {
            PK: applicationDetails.email,
            SK: `${applicationDetails.brand}_${applicationDetails.applicationId}`,
          },
          {
            hasActiveLeadOwner: response?.activeLead === false ? false : true,
          }
        );
      }
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        applicationDetails["product"] = [
          {
            productId: applicationDetails?.productId,
          },
        ];
        if (applicationDetails.applicantType === "Domestic") {
          applicationDetails.identityDocumentObject = [
            {
              identityType:
                applicationDetails.proofOfIdentification === "Canadian Passport"
                  ? "Passport"
                  : "Identity Card",
              identityNumber: applicationDetails.idNo,
              identityExpiryDate: applicationDetails.expiryDate,
            },
          ];
        } else {
          applicationDetails.identityDocumentObject = [
            {
              identityNumber: applicationDetails.passportNumber,
              identityIssueDate: applicationDetails.passportIssueDate,
              identityExpiryDate: applicationDetails.passportExpiryDate,
              identityIssuingCountry:
                applicationDetails.passportIssuingCountryDisplayName,
              identityType: "Passport",
            },
          ];
        }
        const mappings = {
          institutions: "educationHistory",
          priorEmployments: "workHistory",
        };
        for (const [sourceKey, targetKey] of Object.entries(mappings)) {
          if (applicationDetails.hasOwnProperty(sourceKey)) {
            applicationDetails[targetKey] = applicationDetails[sourceKey];
          }
        }
        if (applicationDetails?.institutions?.[0]?.institutionName) {
          applicationDetails.leadInstitutionName =
            applicationDetails?.institutions?.[0]?.institutionName;
        }

        console.log(
          "Education history -->",
          applicationDetails.educationHistory
        );
        applicationDetails.educationHistory =
          applicationDetails.educationHistory || [];

        if (applicationDetails.secondaryInstitutions?.length) {
          applicationDetails.educationHistory = [
            ...applicationDetails.secondaryInstitutions,
            ...applicationDetails.educationHistory,
          ];
        }

        console.log(
          "Education history after secondary edu push -->",
          applicationDetails.educationHistory
        );
        if (applicationDetails.educationHistory?.length) {
          let initialCount = 1;
          applicationDetails.educationHistory =
            applicationDetails.educationHistory.filter(
              (institution) =>
                institution && Object.keys(institution).length > 0
            );
          applicationDetails.educationHistory.forEach((institution) => {
            institution.institutionOrder = initialCount++;
            console.log(
              `Assigned institutionOrder ${institution.institutionOrder} to institution:`,
              JSON.stringify(
                institution.institutionName ||
                  institution.secondaryInstitutionName ||
                  "unnamed"
              )
            );
          });
        }

        referenceMappedDetails = await opportunityFileReferenceMapping(
          applicationDetails
        );

        applicationDetails = referenceMappedDetails.appDetails;

        applicationDetails = {
          ...applicationDetails,
          ...buildMappings(applicationDetails),
        };
      }
      if (referenceMappedDetails?.institutionData) {
        const mappedInstitutionData = {
          ...request,
          ...(await mapSalesforceObject(
            referenceMappedDetails?.institutionData,
            filledBy
          )),
        };
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        await postDataSf(
          mappedInstitutionData,
          APIKEY,
          filledBy,
          isReprocess === true ? true : false
        );
        await postDataSf(
          request,
          APIKEY,
          filledBy,
          isReprocess === true ? true : false
        );
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        response = await postDataSf(
          request,
          APIKEY,
          filledBy,
          isReprocess === true ? true : false
        );
        await dbService.updateObject(
          `gus-oap-student-applications-${process.env.STAGE}`,
          {
            PK: applicationDetails.email,
            SK: `${applicationDetails.brand}_${applicationDetails.applicationId}`,
          },
          {
            hasActiveLeadOwner: response?.activeLead === false ? false : true,
          }
        );
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};
export const formDraftApplicationDetails = async (applicationDetail) => {
  const draftApplication = { ...applicationDetail };
  draftApplication["stage"] = "Application";
  draftApplication["admissionStage"] = "Draft Application";
  draftApplication["isSubmitted"] = false;
  draftApplication["applicationStatus"] = "inProgress";
  delete draftApplication.documents;
  return draftApplication;
};
function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue, key?) => {
      documentIds.forEach((docId) => {
        const matchingDocument = documentMap.get(docId);
        if (matchingDocument) {
          matchingDocument[referenceKey] = referenceValue;
        }
        if (key === "passportDocument") {
          matchingDocument.isIdentityInformationAccurate =
            appDetails?.isIdentityInformationAccurate === "Yes" ? true : false;
        }
      });
    };
    let institutionData = {
      documents: [],
      educationHistory: [],
    };
    if (Array.isArray(appDetails.educationHistory)) {
      appDetails.educationHistory.forEach((institution) => {
        const institutionOrder = institution.institutionOrder;
        const testCertificates =
          institution.testCertificateId || institution.secondaryTransript || [];
        const degreeCertificates = institution.degreeCertificate || [];

        const documentIds = [
          ...testCertificates.map((doc) => doc.documentId),
          ...degreeCertificates.map((doc) => doc.documentId),
        ];

        addReferences(
          documentIds,
          "eduReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );

        institutionData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        institutionData.educationHistory.push(institution);
      });

      appDetails.educationHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !institutionData.documents.some(
            (institutionDoc) => institutionDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }

    let attachReportReferenceValue = "@{LanguageProficiencyRecord__c_2.id}";
    if (!Array.isArray(appDetails.report)) {
      attachReportReferenceValue = "@{LanguageProficiencyRecord__c_1.id}";
    }

    const categories = [
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
      {
        key: "report",
        referenceKey: "testReference",
        referenceValue: "@{LanguageProficiencyRecord__c_1.id}",
      },
      {
        key: "attatchReport",
        referenceKey: "testReference",
        referenceValue: attachReportReferenceValue,
      },
    ];

    categories.forEach(({ key, referenceKey, referenceValue }) => {
      if (Array.isArray(appDetails[key])) {
        const documentIds = appDetails[key].map((doc) => doc.documentId);
        addReferences(documentIds, referenceKey, referenceValue, key);
      }
    });

    console.log("Processed appDetails.documents:", appDetails.documents);
    console.log("Institution data moved:", institutionData);

    return { appDetails, institutionData };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
  };
}
function buildMappings(applicationDetails) {
  try {
    const {
      emergencyContactFirstName,
      emergencyContactSurname,
      emergencyContactRelationship,
      emergencyContactPhoneNumber,
      emergencyContactEmail,
      testName,
      testDate,
      overallScore,
      listeningScore,
      speakingScore,
      readingScore,
      writingScore,
      standardizedAdmissionsTest,
      englishProficiencyLevelOption,
      testDateStandardizedAdmissionTest,
      overallScoreStandardizedAdmissionTest,
      levelDisplayName,
      testNameDisplayName,
      testReportFormNo,
    } = applicationDetails;

    let mappings = {
      connections: [],
      languageProficiency: [
        {
          name: englishProficiencyLevelOption,
          proficiencyQualification: testName,
          provider: testNameDisplayName,
          testDate,
          overallScore,
          listeningScore,
          speakingScore,
          readingScore,
          writingScore,
          testReportFormNo,
        },
      ],
    };
    let emergencyContact = {
      emergencyContactFirstName,
      emergencyContactSurname,
      emergencyContactRelationship,
      emergencyContactPhoneNumber,
      emergencyContactEmail,
    };
    if (Object.values(emergencyContact).some((value) => value)) {
      emergencyContact["connectionType"] = "Emergency";
      mappings.connections.push(emergencyContact);
    }
    // Handle Postgraduate case
    if (levelDisplayName === "Postgraduate") {
      mappings["languageProficiency"].push({
        proficiencyQualification:
          standardizedAdmissionsTest === "None"
            ? null
            : standardizedAdmissionsTest,
        testDate: testDateStandardizedAdmissionTest,
        overallScore: overallScoreStandardizedAdmissionTest,
        name: undefined,
        listeningScore: undefined,
        speakingScore: undefined,
        readingScore: undefined,
        writingScore: undefined,
        provider: undefined,
        testReportFormNo: undefined,
      });
    }

    if (applicationDetails?.grantSelection) {
      mappings["miscDetails"] = JSON.stringify({
        awardName: applicationDetails?.grantSelection,
      });
    }
    console.log("mappings", mappings);
    return mappings;
  } catch (error) {
    throw error;
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig =
    filledBy === "agent" ? salesforceAgentConfig : salesforceStudentConfig;
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      if (applicationDetails[salesforceConfig[object][0]]) {
        for (const record of applicationDetails[salesforceConfig[object][0]]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[salesforceConfig[object][0]]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    }
  }

  return result;
}
