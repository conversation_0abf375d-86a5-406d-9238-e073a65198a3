export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    levelOfStudy: "Education_Level__c",
    institutionName: "InstitutionName__c",
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    studyCompleted: "Study_completed__c",
    countryDisplayName: "Country__c",
    institutionOrder: "Institution_Number__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
    name: "Name",
    provider: "TestProvider__c",
    testReportFormNo: "Test_link_Certification__c",
  },
  workHistory: {
    nameOfOrganization: "Employer__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    positionTitle: "Position__c",
    empyCountryDisplayName: "EmployerAddress__c",
    positionStatus: "Position_Type__c",
    positionLevelDisplayName: "NatureOfDuties__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactSurname: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    emergencyContactEmail: "Email_c__c",
    connectionType: "Type__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
  product: {
    productId: "Product2Id",
  },
};

export const salesforceAgentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  Connection__c: ["connections"],
  WorkHistoryRecord__c: ["workHistory"],
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    countryDisplayName: ["Country__c", "Country"],
    email: "Email",
    preferredCampus: "Location__c",
    opportunityApplicationSource: "ApplicationSource__c",
    gender: "Gender__c",
    institutionName: "Name_of_the_institution__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    leadRecordTypeId: "RecordTypeId",
    "phoneNumber.numberWithCode": "Phone",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    program: "Programme__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    citizenshipDisplayName: "Citizenship__c",
    countryDisplayName: ["Country__c", "PersonMailingCountry"],
    "alternatePhoneNumber.numberWithCode": "PersonOtherPhone",
    middleName: "Middle_Name__c",
    preferredName: "PreferedFirstName__c",
    birthDate: "DateOfBirth__c",
    gender: "Gender__c",
    canadaResidencyStatus: "Citizenship_Status__c",
    primaryLanguage: "Primary_Language__c",
    streetAddress: "PersonMailingStreet",
    city: ["gaconnector_City__c", "PersonMailingCity"],
    postalCode: "PersonMailingPostalCode",
    "phoneNumber.numberWithCode": ["Mobile__c", "PersonMobilePhone"],
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
    passportNumber: "Passport__pc",
    country: "PersonMailingCountryCode",
    permanentStreetAddress: ["ShippingStreet", "BillingStreet"],
    permanentCity: ["ShippingCity", "BillingCity"],
    permanentPostalCode: ["ShippingPostalCode", "BillingPostalCode"],
    permanentCountry: ["ShippingCountryCode", "BillingCountryCode"],
    permanentCountryDisplayName: ["ShippingCountry", "BillingCountry"],
    agentContactUserId: "OwnerId",
    placeOfBirth: "PlaceOfBirth__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Opportunity: {
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountPhone__c",
    preferredCampus: "Location__c",
    city: "gaconnector_City__c",
    agreeToUniversityDisclosure: "DeclarationInfoProvided__c",
    citizenshipDisplayName: "Citizenship__c",
    appId: "ApplicationId__c",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    startTerm: ["CloseDate", "Product_Intake_Date__c", "OverallStartDate__c"],
    program: "Programme__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    admissionStage: "AdmissionsStage__c",
    applicationUpdateSource: "Application_Update_Source__c",
    applicationSubmitSource: "Application_Submission_Source__c",
  },
  // Enquiry: {
  //   firstName: "FirstName__c",
  //   lastName: "AccountLastName__c",
  //   email: "Email__c",
  //   country: "Country__c",
  //   program: "ProgrammeId__c",
  // },
  Application__c: {
    applicationRecordTypeId: "RecordTypeId",
    firstName: "First_Name__c",
    middleName: "Middle_Other_Name_s__c",
    email: "Email__c",
    citizenshipDisplayName: "Citizenship__c",
    birthDate: "Date_of_birth__c",
    gender: "Gender__c",
    streetAddress: "Street_Address__c",
    city: "City__c",
    postalCode: "Postcode__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    applicationId: "Application_Form_Id__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    startTerm: ["Start_Date__c", "Intake__c"],
    fullName: "Name",
    countryDisplayName: "Nationality__c",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    programDisplayName: "Program_Of_Study__c",
    leadInstitutionName: "Institution1__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
  },
  OpportunityLineItem: ["product"],
  OpportunityTeamMember: ["teamMembers"],
};

export const salesforceStudentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    leadInstitutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    levelOfStudy: "Education_Level__c",
    institutionName: "InstitutionName__c",
    secondaryInstitutionName: "InstitutionName__c",
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    studyCompleted: "Study_completed__c",
    countryDisplayName: "Country__c",
    institutionOrder: "Institution_Number__c",
    secondaryCountryDisplayName: "Country__c",
    secondaryLevelOfStudy: "Education_Level__c",
    secondaryStudyCompleted: "Study_completed__c",
    academicCourses: "Specialisation__c",
    secondaryFirstEnrolmentDate: "EnrolmentDateYear__c",
    secondaryLastEnrolmentDate: "GraduationDate__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
    name: "Name",
    provider: "TestProvider__c",
    testReportFormNo: "Test_link_Certification__c",
  },
  workHistory: {
    nameOfOrganization: "Employer__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    positionTitle: "Position__c",
    empyCountryDisplayName: "EmployerAddress__c",
    positionStatus: "Position_Type__c",
    positionLevelDisplayName: "NatureOfDuties__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactSurname: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    emergencyContactEmail: "Email_c__c",
    connectionType: "Type__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
    proofOfIdentification: "Identity_Type__c",
  },
  product: {
    productId: "Product2Id",
  },
};

export const salesforceStudentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  WorkHistoryRecord__c: ["workHistory"],
  Connection__c: ["connections"],
  Lead: {
    firstName: "FirstName",
    lastName: "LastName",
    countryDisplayName: ["Country__c", "Country"],
    email: "Email",
    preferredCampus: "Location__c",
    gender: "Gender__c",
    "phoneNumber.numberWithCode": "Phone",
    opportunityApplicationSource: ["ApplicationSource__c", "Vendor__c"],
    distributeAutomatic: "Distrbute_Automatically__c",
    leadInstitutionName: "Name_of_the_institution__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    program: "Programme__c",
    "utmParams.utmSource": "pi__utm_source__c",
    "utmParams.utmMedium": "pi__utm_medium__c",
    "utmParams.utmCampaign": "pi__utm_campaign__c",
    "utmParams.utmContent": "pi__utm_content__c",
    "utmParams.utmTerm": "pi__utm_term__c",
    "utmParams.utmNetwork": "utm_network__c",
    "utmParams.utmReferrer": "Utm_Referrer__c",
    sourceWebsite: "Source_Website__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    citizenshipDisplayName: "Citizenship__c",
    countryDisplayName: ["Country__c", "PersonMailingCountry"],
    "alternatePhoneNumber.numberWithCode": "PersonOtherPhone",
    middleName: "Middle_Name__c",
    preferredName: "PreferedFirstName__c",
    birthDate: "DateOfBirth__c",
    gender: "Gender__c",
    applicantType: "Type_of_Student__c",
    canadaResidencyStatus: "Citizenship_Status__c",
    primaryLanguage: "Primary_Language__c",
    streetAddress: "PersonMailingStreet",
    city: ["gaconnector_City__c", "PersonMailingCity"],
    postalCode: "PersonMailingPostalCode",
    "phoneNumber.numberWithCode": ["Mobile__c", "PersonMobilePhone"],
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
    passportNumber: "Passport__pc",
    idNo: "Passport__pc",
    country: "PersonMailingCountryCode",
    permanentStreetAddress: ["ShippingStreet", "BillingStreet"],
    permanentCity: ["ShippingCity", "BillingCity"],
    permanentPostalCode: ["ShippingPostalCode", "BillingPostalCode"],
    permanentCountry: ["ShippingCountryCode", "BillingCountryCode"],
    permanentCountryDisplayName: ["ShippingCountry", "BillingCountry"],
    indigenousIdentity: "Demography_Status__c",
    placeOfBirth: "PlaceOfBirth__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Opportunity: {
    miscDetails: "Application_Misc_Details__c",
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountPhone__c",
    preferredCampus: "Location__c",
    city: "gaconnector_City__c",
    agreeToUniversityDisclosure: "DeclarationInfoProvided__c",
    citizenshipDisplayName: "Citizenship__c",
    appId: "ApplicationId__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    startTerm: ["CloseDate", "Product_Intake_Date__c", "OverallStartDate__c"],
    program: "Programme__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    admissionStage: "AdmissionsStage__c",
    applicationUpdateSource: "Application_Update_Source__c",
    applicationSubmitSource: "Application_Submission_Source__c",
  },
  Application__c: {
    applicationRecordTypeId: "RecordTypeId",
    firstName: "First_Name__c",
    middleName: "Middle_Other_Name_s__c",
    email: "Email__c",
    citizenshipDisplayName: "Citizenship__c",
    birthDate: "Date_of_birth__c",
    gender: "Gender__c",
    streetAddress: "Street_Address__c",
    city: "City__c",
    postalCode: "Postcode__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    applicationId: "Application_Form_Id__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    countryDisplayName: "Nationality__c",
    startTerm: ["Start_Date__c", "Intake__c"],
    fullName: "Name",
    programDisplayName: "Program_Of_Study__c",
    leadInstitutionName: "Institution1__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
  },
  OpportunityLineItem: ["product"],
};
