import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "@functions/wul-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleWulSfSaveOrUpdateRequests = async (event) => {
  const brand = "WUL";

  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let referenceMappedDetails;

    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent = !applicationDetails?.privacyPolicyConsent;
    }

    if (applicationDetails.isPermanentAddressDiffer === "Yes") {
       applicationDetails.billingStreet = applicationDetails.street
       applicationDetails.billingCity = applicationDetails.city
       applicationDetails.billingPostalCode = applicationDetails.postalCode
       applicationDetails.billingState = applicationDetails.stateDisplayName
       applicationDetails.billingCountryDisplayName = applicationDetails.countryDisplayName
 
       applicationDetails.street = applicationDetails.permanentStreet
       applicationDetails.city = applicationDetails.permanentCity
       applicationDetails.postalCode = applicationDetails.permanentPostalCode
       applicationDetails.stateDisplayName = applicationDetails.permanentStateDisplayName
       applicationDetails.countryDisplayName = applicationDetails.permanentCountryDisplayName
    }

    try {
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        try {
          applicationDetails["product"] = [
            {
              productId: applicationDetails.productId,
            },
          ];
          const mappings = {
            institutions: "educationHistory",
            workExperience: "workHistory",
            languageProficiencyScores: "languageProficiency"
          };

          for (const [sourceKey, targetKey] of Object.entries(mappings)) {
            if (applicationDetails.hasOwnProperty(sourceKey)) {
              applicationDetails[targetKey] = applicationDetails[sourceKey];
            }
          }

          //lead institution name updation
          if (applicationDetails?.institutions?.[0]) {
            applicationDetails = { ...applicationDetails, ...applicationDetails.institutions[0] }

          }

          if (applicationDetails?.workHistory?.[0]) {
            applicationDetails = { ...applicationDetails, ...applicationDetails.workHistory[0] }
          }

          applicationDetails.identityDocumentObject = [
            {
              identityNumber: applicationDetails?.passportNumber,
              identityIssueDate: applicationDetails?.passportIssueDate,
              identityExpiryDate: applicationDetails?.passportExpiryDate,
              identityIssuingCountry:
                applicationDetails?.passportIssuingCountryDisplayName,
              identityType: "Passport",
            },
          ];

          if (applicationDetails.educationHistory?.length) {
            let initialCount = 1;
            applicationDetails.educationHistory =
              applicationDetails.educationHistory.filter(
                (institution) =>
                  institution && Object.keys(institution).length > 0
              );
            applicationDetails.educationHistory.forEach((institution) => {
              institution.institutionOrder = initialCount++;
            });
          }

          referenceMappedDetails = await opportunityFileReferenceMapping(applicationDetails);

          applicationDetails?.documents.forEach((doc) => {
            if (doc.documentType === 'ID/Passport') {
              doc.isIdentityInformationAccurate =
                applicationDetails.isIdentityInformationAccurate === "Yes";
            }
            if (doc.documentType === 'Application form') {
              doc.status = "Auto-accepted";
            }
          });

          applicationDetails = referenceMappedDetails.appDetails;
        } catch (error) {
          throw error;
        }
      } else {
        console.log('Skipping mapping - application not submitted or already sent to Salesforce');
      }
      applicationDetails.birthPlace = [applicationDetails?.birthCity, applicationDetails?.birthStateDisplayName]
        .filter(Boolean)
        .join(', ');

      applicationDetails.citizenshipStatus = applicationDetails?.residencyStatus === true ? "Permanent Residency" : null;

      applicationDetails.ethnicity = applicationDetails?.isHispanicOrLatino === "Yes" ? "Hispanic or Latino" : null;
      console.log('AppDetails ->', applicationDetails)
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;

      if (referenceMappedDetails?.institutionData ||
        referenceMappedDetails?.testData) {
        const mappedData = await Promise.all([
          referenceMappedDetails?.institutionData
            ? mapSalesforceObject(
                referenceMappedDetails.institutionData,
                filledBy
              )
            : null,
          referenceMappedDetails?.testData
            ? mapSalesforceObject(referenceMappedDetails.testData, filledBy)
            : null,
          mapSalesforceObject(applicationDetails, filledBy),
        ]);
        const [mappedInstitutionData, mappedTestData, mappedRequest] =
          mappedData;
        await Promise.all([
          mappedInstitutionData &&
            postDataSf(
              { ...request, ...mappedInstitutionData },
              APIKEY,
              filledBy
            ),
          mappedTestData &&
          postDataSf({ ...request, ...mappedTestData }, APIKEY, filledBy),
          postDataSf({ ...request, ...mappedRequest }, APIKEY, filledBy),
        ]);
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        await postDataSf(request, APIKEY, filledBy);
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        request,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      console.log(error);
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue, key?) => {
      documentIds.forEach((docId) => {
        const matchingDocument = documentMap.get(docId);
        if (matchingDocument) {
          matchingDocument[referenceKey] = referenceValue;
        }
        if (key === "passportDocument") {
          matchingDocument.isIdentityInformationAccurate =
            appDetails?.isIdentityInformationAccurate === "Yes" ? true : false;
        }
      });
    };

    let institutionData = {
      documents: [],
      educationHistory: [],
    };

    let testData = {
      documents: [],
      languageProficiency: [],
    };

    if (Array.isArray(appDetails.educationHistory)) {
      appDetails.educationHistory.forEach((institution) => {
        const institutionOrder = institution.institutionOrder;
        const institutionCertificates = institution.institutionCertificate || [];

        const documentIds = institutionCertificates.map((doc) => doc.documentId);

        addReferences(
          documentIds,
          "educationReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );

        institutionData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        institutionData.educationHistory.push(institution);
      });

      appDetails.educationHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !institutionData.documents.some(
            (institutionDoc) => institutionDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }

    if (Array.isArray(appDetails.languageProficiency)) {
      appDetails.languageProficiency.forEach((test, index) => {
        const testOrder = index + 1;
        const testCertificateId = test.testReport || [];

        const documentIds = [...testCertificateId.map((doc) => doc.documentId)];

        addReferences(
          documentIds,
          "testReference",
          `@{LanguageProficiencyRecord__c_${testOrder}.id}`
        );

        testData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        testData.languageProficiency.push(test);
      });

      appDetails.languageProficiency = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !testData.documents.some(
            (testDoc) => testDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No test data found in appDetails. Nothing to process.");
    }


    const categories = [
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      }
    ];

    categories.forEach(({ key, referenceKey, referenceValue }) => {
      if (Array.isArray(appDetails[key])) {
        const documentIds = appDetails[key].map((doc) => doc.documentId);
        addReferences(documentIds, referenceKey, referenceValue, key);
      } else {
        console.log(`No documents found for category: ${key}`);
      }
    });
    return { appDetails, institutionData, testData};
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
    testData: { documents: [], languageProficiency: [] },
  };
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig =
    filledBy === "agent" ? salesforceAgentConfig : salesforceStudentConfig;
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;

  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      const subObjectKey = salesforceConfig[object][0];
      if (applicationDetails[subObjectKey]) {
        for (const record of applicationDetails[subObjectKey]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[subObjectKey]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      } else {
        console.log(`No data found for sub-object key: ${subObjectKey}`);
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    } else {
      console.log(`No value found for source key: ${sourceKey}`);
    }
  }
  return result;
}
