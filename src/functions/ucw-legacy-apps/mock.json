{"Records": [{"messageId": "99a98bd6-e41b-4fda-8b9e-e4a5ce320a46", "receiptHandle": "AQEBHgkx1oIZUBcYZALMuePyqCvbXWnE+Eu9RRUCdt9yxLaXgIUaZeafmxIVXeDoUsvLk0BIeprSMquDQuAj+ZnO23KRZKaNv91eNUn8CdMAQ76zRfwqY7G01dybKTwVg3LvdWzsM/O7DvTPXLCs9lTDeq5If17KGKfDnZFD9sP9VIqQXJ/dKajrsCt6Z/gbwQwD9o78fOqFZ+mXqtc/etQR37wGrhSpgyeAOeeyHfgFdDEM5mt66DfA3vvQxvt/6gG188IVovkWbLSEvIRS14tQi0ik5wgiO2JXmlJ/xxsqRXKH9Mcv7ms173HeojzXQcz+", "body": "{\"_id\":\"675b05c543b7db81e20fd832\",\"uuid\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\",\"userId\":20291,\"sfProgramId\":1477,\"agentLinkId\":null,\"sfAgentId\":null,\"data\":{\"state\":\"Telangana\",\"termsAccept\":[\"0\"],\"desclarationsList\":[\"0\",\"1\",\"2\",\"3\",\"4\"],\"fileAwardsPersonalStatement\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Personal Statement.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Personal statement\\/8994a2a0e96277052018e28300b4823d.pdf\"}],\"awardsApply\":\"1\",\"fileOtherCertification\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Other Documents.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Other certification\\/0f8c380c5f0e20a00ccceb010510cff1.pdf\"}],\"checklistCredentials\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Degree Certificate.png\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Degree certificate\\/12c78d931a94aee0d271da1cef844a2f.png\"}],\"fileCV\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Sample CV.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/CV\\/32b75756a92b9dc881648b0e4d21118b.pdf\"}],\"employmentCompanies\":[{\"employmentLevel\":\"management\",\"employmentStatus\":\"permanentFull\",\"employmentTitle\":\"BAS\",\"employmentEmployer\":\"ABC\",\"employmentCountry\":\"Canada\",\"employmentFrom\":\"2020-01-14T00:00:00+00:00\",\"employmentTo\":\"2023-01-03T00:00:00+00:00\"}],\"employmentVolunteer\":\"1\",\"gmatOrGreFile\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"GRE - GMA.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Other certification\\/bc0b4733da72a9571b042f4d96d976dc.pdf\"}],\"gmatOrGreDate\":\"2024-12-29T00:00:00+00:00\",\"gmatOrGreScore\":\"200\",\"gmatOrGreType\":\"gre\",\"englishTestFile\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Standardized English Test 1.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Proof English proficiency\\/3385e8042d50b6a3fc0a92f2e4d51bea.pdf\"}],\"englishTestReportNumber\":\"TEHC6736\",\"englishTestScoreWriting\":\"20\",\"englishTestScoreReading\":\"20\",\"englishTestScoreSpeaking\":\"20\",\"englishTestScoreListening\":\"20\",\"englishTestScore\":\"100\",\"englishTestDate\":\"2024-12-30T00:00:00+00:00\",\"englishTestProficiency\":\"ielts\",\"englishTest\":\"standardizedTest\",\"educationWithdraw\":\"0\",\"educationSchools\":[{\"educationSchoolsCountry\":\"United Kingdom\",\"educationSchoolsStudyLevel\":\"bachelorDegree\",\"educationSchoolsIsCompleted\":\"yes\",\"educationSchoolsName\":\"ABNC University\",\"educationSchoolsFirstEnrolmentYear\":\"2014\",\"educationSchoolsLastEnrolmentYear\":\"2020\",\"educationSchoolsFile\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Degree Transcript 1.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Degree transcripts\\/171953fb9ff6dad1cb0ab393046e3f72.pdf\"}]},{\"educationSchoolsCountry\":\"Germany\",\"educationSchoolsStudyLevel\":\"postGraduateCertificate\",\"educationSchoolsIsCompleted\":\"yes\",\"educationSchoolsName\":\"QWRRT\",\"educationSchoolsFirstEnrolmentYear\":\"2021\",\"educationSchoolsLastEnrolmentYear\":\"2024\",\"educationSchoolsFile\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Degree Transcript 2.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Degree transcripts\\/4216cbe6e2a3b5ec57369c05e7cd38d5.pdf\"}]}],\"emergencyEmail\":\"<EMAIL>\",\"emergencyPhone\":\"+9175648-37465\",\"emergencyRelationship\":\"spouse\",\"emergencyFamilyName\":\"DEF\",\"emergencyFirstName\":\"ABC\",\"altPhone\":\"+9133432-43242\",\"permanentAddress\":\"0\",\"postalCode\":\"500013\",\"country\":\"India\",\"city\":\"Hyderabad\",\"mailingAddress\":\"2-3-54\\/11\\/b\\/4 Quadri Bagh Amberpet\",\"disability\":\"0\",\"firstLanguage\":\"English\",\"citizenshipExpireDate\":\"2058-12-30T00:00:00+00:00\",\"citizenshipOther\":\"workPermit\",\"filePassport\":[{\"s3BucketName\":\"reviewcenter-stage\",\"originalFileName\":\"Passport 1.pdf\",\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/ID\\/Passport\\/c99918a6cc6766b7a2137d49d3699d51.pdf\"}],\"aboriginal\":\"0\",\"citizenship\":\"Canada\",\"gender\":\"male\",\"dateOfBirth\":\"1994-01-12T00:00:00+00:00\",\"preferredFirstName\":\"UAT\",\"middleName\":\"na\",\"startTermOnline\":\"01tTf000002McLd\",\"location\":\"Distance Learning\",\"agreementAccept\":[\"0\"],\"firstName\":\"Uat\",\"legalFamilyName\":\"Test_1\",\"personalEmail\":\"<EMAIL>\",\"phone\":\"+************\",\"program\":\"a010X00000yUORYQA4\",\"isExistPathwayPartner\":false},\"additionalData\":null,\"configChanges\":[],\"intakeFilters\":null,\"createdByApiFields\":null,\"studentMainData\":{\"email\":\"<EMAIL>\",\"firstName\":\"Uat\",\"lastName\":\"Test_1\",\"phone\":\"+************\",\"country\":\"Canada\"},\"pdf\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Application form\\/application_form.pdf\",\"pdfInfo\":{\"filePath\":\"83bfce30-b8a0-11ef-9461-5ac2897f34f4\\/Application form\\/application_form.pdf\",\"originalFileName\":\"application_form.pdf\",\"s3BucketName\":\"reviewcenter-stage\"},\"type\":\"base\",\"language\":\"us\",\"anonymousTokenSecret\":null,\"anonymousSubmitToken\":null,\"isReviewed\":false,\"isFinishPageShown\":true,\"isSubmitted\":true,\"submittedAt\":\"2025-01-27T10:51:15.701+00:00\",\"emailResentAt\":null,\"rcStatus\":null,\"appStatus\":null,\"hasSysPayment\":false,\"createdAt\":\"2024-12-12T15:48:21.664+00:00\",\"updatedAt\":\"2025-01-27T10:51:44.209+00:00\",\"promoCodeId\":null,\"paymentStatus\":null,\"paymentType\":null,\"payedSum\":null,\"paymentTransactionId\":null,\"paymentUuid\":null,\"cachedStat\":{\"hashConfig\":\"4c8c40a82573c7fefcdc3f6d1950a5e8\",\"hashSavedField\":\"679126373265d1f609bbe884adac1250\",\"updatedAt\":\"2024-12-17T13:43:05+00:00\",\"percent\":14,\"steps\":[{\"title\":\"Agreement\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Program Information\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Applicant Information\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Emergency Contact\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Education History\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Standardized Tests\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Employment History\",\"isStepCompleted\":true,\"visible\":true},{\"title\":\"Additional Documents\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Awards\",\"isStepCompleted\":true,\"visible\":true},{\"title\":\"Declarations\",\"isStepCompleted\":false,\"visible\":true},{\"title\":\"Terms and Conditions\",\"isStepCompleted\":false,\"visible\":true}]}}", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "1737975104891", "SequenceNumber": "18891665700561647616", "MessageGroupId": "83bfce30-b8a0-11ef-9461-5ac2897f34f4", "SenderId": "AIDAWYJAWPFU7SUQGUJC6", "MessageDeduplicationId": "173797510467976540371ba", "ApproximateFirstReceiveTimestamp": "1737975104891"}, "messageAttributes": {}, "md5OfBody": "44d6b319e5626954cbb40ead0bf42306", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:eu-west-1:933713876074:DEV-UCW-LEGACY-APPLICATION-QUEUE.fifo", "awsRegion": "eu-west-1"}]}