import {
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "./salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleUcwSfSaveOrUpdateRequests = async (event) => {
  const brand = "UCW";
  let applicationDetails = event;
  console.log("App details -->", applicationDetails);
  const requestId = applicationDetails.requestId;
  const filledBy = applicationDetails?.applicationFilledBy;
  const APIKEY = applicationDetails.APIKEY;
  const currentUTC = new Date().toISOString();
  let request = {};
  console.log(
    "Modified Application details document -->",
    applicationDetails.documents
  );
  try {
    if (
      !applicationDetails?.isPermanentAddressDiffer ||
      applicationDetails?.isPermanentAddressDiffer === "No"
    ) {
      applicationDetails = {
        ...applicationDetails,
        permanentStreetAddress: applicationDetails?.streetAddress,
        permanentCity: applicationDetails?.city,
        permanentPostalCode: applicationDetails?.postalCode,
        permanentCountry: applicationDetails?.country,
        permanentCountryDisplayName: applicationDetails?.countryDisplayName,
      };
    }
    if (
      applicationDetails?.applicationStatus === "submitted" &&
      !applicationDetails?.submittedToSf
    ) {
      const mappings = {
        institutions: "educationHistory",
        priorEmployments: "workHistory",
      };
      for (const [sourceKey, targetKey] of Object.entries(mappings)) {
        if (applicationDetails.hasOwnProperty(sourceKey)) {
          applicationDetails[targetKey] = applicationDetails[sourceKey];
        }
      }
      if (applicationDetails?.institutions[0]?.institutionName) {
        applicationDetails.leadInstitutionName =
          applicationDetails?.institutions[0]?.institutionName;
      }
      applicationDetails["levelDisplayName"] =
        applicationDetails.program === "a010X00000yUORYQA4"
          ? "Postgraduate"
          : "Undergraduate";
      applicationDetails = {
        ...applicationDetails,
        ...buildMappings(applicationDetails),
      };
    }
    request["sectionLabel"] = applicationDetails.sectionLabel;
    request["email"] = applicationDetails.email;
    request["applicationId"] = applicationDetails.applicationId;
    request["requestId"] = requestId;
    const identityDocumentObject = {
      identityNumber: applicationDetails.passportNumber,
      identityIssueDate: applicationDetails.passportIssueDate,
      identityExpiryDate: applicationDetails.passportExpiryDate,
      identityIssuingCountry:
        applicationDetails.passportIssuingCountryDisplayName,
      identityType: "Passport",
    };
    applicationDetails.identityDocumentObject = identityDocumentObject;
    request = {
      ...request,
      ...(await mapSalesforceObject(applicationDetails, filledBy)),
    };
    console.log("sf request -->", request);
    await postDataSf(request, APIKEY, filledBy);
  } catch (error) {
    throw error;
  }
};
function buildMappings(applicationDetails) {
  try {
    const {
      emergencyContactFirstName,
      emergencyContactSurname,
      emergencyContactRelationship,
      emergencyContactPhoneNumber,
      emergencyContactEmail,
      testName,
      testDate,
      overallScore,
      listeningScore,
      speakingScore,
      readingScore,
      writingScore,
      standardizedAdmissionsTest,
      englishProficiencyLevelOption,
      testDateStandardizedAdmissionTest,
      overallScoreStandardizedAdmissionTest,
      levelDisplayName,
      testNameDisplayName,
    } = applicationDetails;

    let mappings = {
      connections: [
        {
          emergencyContactFirstName,
          emergencyContactSurname,
          emergencyContactRelationship,
          emergencyContactPhoneNumber,
          emergencyContactEmail,
          connectionType: "Emergency",
        },
      ],
      languageProficiency: [
        {
          name: englishProficiencyLevelOption,
          proficiencyQualification: testName,
          provider: testNameDisplayName?.startsWith("TOEFL")
            ? testNameDisplayName.match(/\(([^)]+)\)/)?.[1]
            : undefined,
          testDate,
          overallScore,
          listeningScore,
          speakingScore,
          readingScore,
          writingScore,
        },
      ],
    };

    // Handle Postgraduate case
    if (levelDisplayName === "Postgraduate") {
      mappings["languageProficiency"].push({
        proficiencyQualification: standardizedAdmissionsTest,
        testDate: testDateStandardizedAdmissionTest,
        overallScore: overallScoreStandardizedAdmissionTest,
        name: undefined,
        listeningScore: undefined,
        speakingScore: undefined,
        readingScore: undefined,
        writingScore: undefined,
        provider: undefined,
      });
    }

    return mappings;
  } catch (error) {
    throw error;
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig = salesforceStudentConfig;
  const salesforceSubObjectsConfig = salesforceStudentSubObjectsConfig;
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      if (applicationDetails[salesforceConfig[object][0]]) {
        for (const record of applicationDetails[salesforceConfig[object][0]]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[salesforceConfig[object][0]]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    }
  }

  return result;
}
