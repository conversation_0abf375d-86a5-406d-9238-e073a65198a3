export const mappingSubObjects = {
    institutions: {
        country: "educationSchoolsCountry",
        levelOfStudy: "educationSchoolsStudyLevel",
        studyCompleted: "educationSchoolsIsCompleted",
        institutionName: "educationSchoolsName",
        firstEnrolmentDate: "educationSchoolsFirstEnrolmentYear",
        lastEnrolmentDate: "educationSchoolsLastEnrolmentYear",
        countryDisplayName: "educationSchoolsCountry",
        levelOfStudyDisplayName: "educationSchoolsStudyLevel",
        studyCompletedDisplayName: "educationSchoolsIsCompleted"
    },
    priorEmployments: {
        positionLevelDisplayName: "employmentLevel",
        positionLevel: "employmentLevel",
        positionStatus: "employmentStatus",
        positionTitle: "employmentTitle",
        nameOfOrganization: "employmentEmployer",
        country: "employmentCountry",
        startDate: "employmentFrom",
        endDate: "employmentTo",
        countryDisplayName: "employmentCountry"
    },
    opportunityFileMapping: {
        documentType: "documentType",
        eduReference: "eduReference",
        testReference: "testReference",
        originalFileName: "documentName",
        filePath: "path",
        s3BucketName: "bucketName",
        applicationId: "applicationId",
    }
};
export const documentTypeMapping = {
    fileAwardsAdditionalPersonalStatement: "Personal Statement",
    fileOtherCertification: "Other certification",
    checklistCredentials: "Degree certificate",
    fileCV: "CV",
    englishTestFile: "Proof English proficiency",
    filePassport: "ID/Passport",
    gmatOrGreFile: "GMAT - GRE",
    "educationSchools.educationSchoolsFile": "Degree Transcripts",
    pdfInfo: "Application form"
};

export const mappingobjects = {
    "email": "data.personalEmail",
    "applicationId": "uuid",
    "emergencyContactFirstName": "data.emergencyFirstName",
    "emergencyContactSurname": "data.emergencyFamilyName",
    "emergencyContactRelationshipDisplayName": "data.emergencyRelationship",
    "emergencyContactRelationship": "data.emergencyRelationship",
    "emergencyContactPhoneNumber": { "numberWithCode": "data.emergencyPhone" },
    "emergencyContactEmail": "data.emergencyEmail",
    "institutions": "data.educationSchools",
    "englishProficiencyLevelOptionDisplayName": "data.englishTest",
    "englishProficiencyLevelOption": "data.englishTest",
    "testDate": "data.englishTestDate",
    "testName": "data.englishTestProficiency",
    "testNameDisplayName": "data.englishTestProficiency",
    "overallScore": "data.englishTestScore",
    "report": "data.englishTestFile",
    "listeningScore": "data.englishTestScoreListening",
    "speakingScore": "data.englishTestScoreSpeaking",
    "readingScore": "data.englishTestScoreReading",
    "writingScore": "data.englishTestScoreWriting",
    "testReportForm(TRF)no": "data.englishTestReportNumber",
    "standardizedAdmissionsTestDisplayName": "data.gmatOrGreType",
    "standardizedAdmissionsTest": "data.gmatOrGreType",
    "testDateStandardizedAdmissionTest": "data.gmatOrGreDate",
    "overallScoreStandardizedAdmissionTest": "data.gmatOrGreScore",
    "priorEmployments": "data.employmentCompanies",
    "personalStatement": "data.fileAwardsPersonalStatement",
    "program": "data.program",
};

// partnerInstitution