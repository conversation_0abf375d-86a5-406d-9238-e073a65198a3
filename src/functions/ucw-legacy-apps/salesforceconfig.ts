export const salesforceStudentSubObjectsConfig = {
  educationHistory: {
    levelOfStudy: "Education_Level__c",
    institutionName: "InstitutionName__c",
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    studyCompleted: "Study_completed__c",
    countryDisplayName: "Country__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
    name: "Name",
    provider: "TestProvider__c",
  },
  workHistory: {
    nameOfOrganization: "Employer__c",
    startDate: "StartDate__c",
    endDate: "EndDate__c",
    positionTitle: "Position__c",
    countryDisplayName: "EmployerAddress__c",
    positionStatus: "Position_Type__c",
    positionLevelDisplayName: "NatureOfDuties__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactSurname: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    emergencyContactEmail: "Email_c__c",
    connectionType: "Type__c",
  },
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c"
  },
};

export const salesforceStudentConfig = {
  EducationHistoryRecord__c: ["educationHistory"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  OpportunityFile__c: ["documents"],
  WorkHistoryRecord__c: ["workHistory"],
  Connection__c: ["connections"],
  IdentityInfoRecord__c: {
    "identityDocumentObject.identityNumber": "Identity_Document_Number__c",
    "identityDocumentObject.identityIssueDate": "Issue_Date__c",
    "identityDocumentObject.identityExpiryDate": "Expiry_Date__c",
    "identityDocumentObject.identityType": "Identity_Type__c",
    "identityDocumentObject.identityIssuingCountry": "Issuing_Country__c",
  },
};
