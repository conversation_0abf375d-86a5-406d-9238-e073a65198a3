import { handlerPath } from '@libs/handler-resolver';

export const ucwLegacyAppHandler = {
    handler: `${handlerPath(__dirname)}/ucwLegacyAppHandler.handleAppRequests`,
    name: 'ucw-legacy-application-processor-${self:provider.stage}',
    events: [
        {
            sqs: '${self:provider.environment.UCW_LEGACY_APPLICATION_QUEUE_ARN}',
        }
    ],
    timeout: 60
};

export const ucwLegacyReviewedAppHandler = {
    handler: `${handlerPath(__dirname)}/ucwLegacyAppHandler.ucwLegacyReviewedAppRequests`,
    name: 'ucw-legacy-reviewed-application-processor-${self:provider.stage}',
    timeout: 60
};