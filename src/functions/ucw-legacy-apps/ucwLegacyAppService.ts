import {
  mappingobjects,
  mappingSubObjects,
  documentTypeMapping,
} from "./config";
import { publishMessageToSNS } from "@functions/student-service/studentOapLeadChangeService";
import { v4 as uuidv4 } from "uuid";
import { picklistMappings } from "./picklist";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
import { DynamoDBService } from "src/common/dynamodbService";
import * as AWS from "aws-sdk";
import { handleUcwSfSaveOrUpdateRequests } from "./ucwService";
import { getEipData, postData } from "src/connectors/salesforce-connectors";

const csvParser = require("csv-parser");
const s3 = new AWS.S3();
const dynamodbService = new DynamoDBService();
const loggerEnum = new LoggerEnum();
const stage = process.env.STAGE;
const BUCKET_NAME = `ucw-migration-applications-${stage}`;
const FOLDER_NAME = "SubmittedLegacyAppJsons";

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);

export const handleUCWLegacyAppRequests = async (event) => {
  console.log("Event -->", JSON.stringify(event));
  const requestId = uuidv4();
  let studentDetails;
  for (let record of event.Records) {
    console.log("Record -->", record);
    let eventDetails = JSON.parse(record["body"]);
    console.log("Event details ->", JSON.stringify(eventDetails));
    try {
      const checkProcessedRecord = await dynamodbService.getObject(
        `ucw-legacy-applications-${stage}`,
        { PK: "UCW_LEGACY_APPS", SK: eventDetails?.uuid }
      );

      console.log("checkProcessedRecord", checkProcessedRecord);
      studentDetails = await transformInputData(eventDetails);
      studentDetails["requestId"] = requestId;
      studentDetails["APIKEY"] = process.env.UCW_KEY;
      console.log("studentDetails", studentDetails);
      console.log("student details -->", studentDetails);

      studentDetails.submittedToSf = !!checkProcessedRecord.Item;

      await handleUcwSfSaveOrUpdateRequests(studentDetails);
      await dynamodbService.putObject(`ucw-legacy-applications-${stage}`, {
        PK: "UCW_LEGACY_APPS",
        SK: eventDetails.uuid,
        applicationId: eventDetails.uuid,
        status: "Success",
        appDetails: eventDetails,
      });
      if (!checkProcessedRecord.Item) {
        await updateOpportunity(eventDetails?.uuid, {
          ApplicationProgress__c: 100,
          ApplicationSubmitted__c: true,
          StageName: "Documents Stage",
          AdmissionsStage__c: "Application submitted",
        });
      }
      await cloudWatchLoggerService.log(
        requestId,
        new Date().toISOString(),
        loggerEnum.Component.OAP_HANDLERS,
        "UCW_LEGACY_APPLICATION_QUEUE",
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        "",
        eventDetails,
        studentDetails,
        `Save to salesforce`,
        "UCW",
        eventDetails?.data?.personalEmail,
        `oap-handlers/${eventDetails.uuid}/${requestId}`,
        "EducationHistoryRecord_LanguageProficiency_WorkHistory_Connections_IndentityInfoRecord",
        eventDetails.uuid,
        "",
        eventDetails.uuid
      );
    } catch (error) {
      const isValidError = error.errorDetails
        ? checkValidErrorMessage(error?.errorDetails)
        : false;
      await dynamodbService.putObject(`ucw-legacy-applications-${stage}`, {
        PK: "UCW_LEGACY_APPS",
        SK: eventDetails.uuid,
        applicationId: eventDetails.uuid,
        status: isValidError ? "Success" : "Failed",
        appDetails: eventDetails,
      });
      console.log("Error ->", error);
      if (!isValidError) {
        await cloudWatchLoggerService.error(
          requestId,
          new Date().toISOString(),
          loggerEnum.Component.OAP_HANDLERS,
          "UCW_LEGACY_APPLICATION_QUEUE",
          loggerEnum.Component.GUS_EIP_SERVICE,
          loggerEnum.Event.OPERATION_COMPLETED,
          "",
          eventDetails,
          studentDetails,
          error.message ? error.message : JSON.stringify(error),
          "UCW",
          eventDetails?.data?.personalEmail,
          `oap-handlers/${eventDetails.uuid}/${requestId}`,
          "EducationHistoryRecord_LanguageProficiency_WorkHistory_Connections_IndentityInfoRecord",
          eventDetails.uuid,
          "",
          eventDetails.uuid
        );

        throw error;
      }
    }
  }
};

const checkValidErrorMessage = (errorDetails) => {
  console.log("errorDetails", errorDetails);
  try {
    // Extract the JSON part from the error message
    const jsonStartIndex = errorDetails.indexOf('[{"message":');
    if (jsonStartIndex === -1) {
      console.log("No JSON structure found.");
      return false;
    }

    const jsonString = errorDetails.substring(jsonStartIndex);
    const parsedErrors = JSON.parse(jsonString);

    console.log("parsedErrors", parsedErrors);

    if (
      parsedErrors.length &&
      parsedErrors.every((item: any) =>
        item.message.includes(
          "Added identical opportunity file for opportunity"
        )
      )
    ) {
      console.log(
        'Only "Added identical opportunity file for opportunity" issue found.'
      );
      return true;
    }
  } catch (error) {
    console.error("Error parsing JSON:", error);
  }

  console.log("Other errors found or invalid structure.");
  return false;
};

const updateOpportunity = async (applicationId, data) => {
  const opportunityResponse = await getEipData(
    `gus/opportunityId/${applicationId}`,
    "",
    process.env.UCW_KEY
  );

  console.log(opportunityResponse);

  const updateOpportunity = await postData(
    `gus/updateOpportunity/${opportunityResponse.Id}`,
    data,
    process.env.UCW_KEY
  );

  console.log(updateOpportunity);
};

function setIfNotNullOrUndefined(obj, key, value) {
  if (value !== null && value !== undefined) {
    obj[key] = value;
  }
}

async function transformInputData(input: any) {
  try {
    let output: any = {
      documents: [],
    };
    Object.keys(documentTypeMapping).forEach((key) => {
      console.log("key -->>", key);
      console.log(input.data[key]);
      if (input.data[key]) {
        const documentType = documentTypeMapping[key];
        if (Array.isArray(input.data[key])) {
          input.data[key].forEach((item) => {
            const transformedItem = {
              [mappingSubObjects.opportunityFileMapping.documentType]:
                documentType,
              applicationId: input.uuid,
            };
            if (key === "englishTestFile" || key === "gmatOrGreFile") {
              const recordIndex =
                key === "gmatOrGreFile" &&
                input.data?.["englishTestFile"]?.length > 0
                  ? 2
                  : 1;
              transformedItem[
                "testReference"
              ] = `@{LanguageProficiencyRecord__c_${recordIndex}.id}`;
            }
            Object.keys(item).forEach((subKey) => {
              if (
                Array.isArray(mappingSubObjects.opportunityFileMapping[subKey])
              ) {
                mappingSubObjects.opportunityFileMapping[subKey].forEach(
                  (mappedKey) => {
                    transformedItem[mappedKey] = item[subKey];
                  }
                );
              } else if (mappingSubObjects.opportunityFileMapping[subKey]) {
                transformedItem[
                  mappingSubObjects.opportunityFileMapping[subKey]
                ] = item[subKey];
              }
            });
            output.documents.push(transformedItem);
          });
        }
      } else if (input[key]) {
        const transformedItem = {
          [mappingSubObjects.opportunityFileMapping.documentType]:
            documentTypeMapping[key],
          applicationId: input.uuid,
        };
        Object.keys(input[key]).forEach((subKey) => {
          if (Array.isArray(mappingSubObjects.opportunityFileMapping[subKey])) {
            mappingSubObjects.opportunityFileMapping[subKey].forEach(
              (mappedKey) => {
                transformedItem[mappedKey] = input[key][subKey];
              }
            );
          } else if (mappingSubObjects.opportunityFileMapping[subKey]) {
            transformedItem[mappingSubObjects.opportunityFileMapping[subKey]] =
              input[key][subKey];
          }
        });
        output.documents.push(transformedItem);
      }
      if (
        key === "educationSchools.educationSchoolsFile" &&
        input.data["educationSchools"]
      ) {
        let insCount = 0;
        input.data["educationSchools"].forEach((item) => {
          insCount++;
          item.educationSchoolsFile.institutionOrder = insCount;
          item.educationSchoolsFile.forEach((file) => {
            const educationFileItem = {
              [mappingSubObjects.opportunityFileMapping.documentType]:
                documentTypeMapping["educationSchools.educationSchoolsFile"],
              ["eduReference"]: `@{EducationHistoryRecord__c_${insCount}.id}`,
              applicationId: input.uuid,
            };
            Object.keys(file).forEach((fileKey) => {
              if (mappingSubObjects.opportunityFileMapping[fileKey]) {
                educationFileItem[
                  mappingSubObjects.opportunityFileMapping[fileKey]
                ] = file[fileKey];
              }
            });
            output.documents.push(educationFileItem);
          });
        });
      }
    });
    Object.entries(mappingobjects).forEach(([key, value]) => {
      let sourceValue;

      if (typeof value === "object" && !Array.isArray(value)) {
        output[key] = {};
        Object.entries(value).forEach(([subKey, subValue]) => {
          sourceValue = getValue(input, subValue as string);
          console.log(
            `Mapping field '${subKey}', path '${subValue}', Result:`,
            sourceValue
          );
          setIfNotNullOrUndefined(output[key], subKey, sourceValue);
        });
      } else {
        sourceValue = getValue(input, value);
        console.log(`Mapping key: ${key}, Source value:`, sourceValue);

        if (Array.isArray(sourceValue) && mappingSubObjects[key]) {
          output[key] = sourceValue
            .map((item, index) => {
              const mappedItem: any = {};
              console.log(`Mapping array item ${index} for key ${key}:`, item);

              Object.entries(mappingSubObjects[key]).forEach(
                ([subKey, subValue]) => {
                  if (Array.isArray(subValue)) {
                    mappedItem[subKey] = subValue
                      .map((pathObj) => {
                        const result = getValue(item, pathObj.documentId);
                        console.log(
                          `Mapping array field '${subKey}', path '${pathObj.documentId}', Result:`,
                          result
                        );
                        return result;
                      })
                      .filter(
                        (result) => result !== null && result !== undefined
                      );
                  } else {
                    const result = getValue(item, subValue as string);
                    console.log(
                      `Mapping field '${subKey}', path '${subValue}', Result:`,
                      result
                    );
                    setIfNotNullOrUndefined(mappedItem, subKey, result);
                  }
                }
              );
              return mappedItem;
            })
            .filter((item) => Object.keys(item).length > 0);
        } else {
          setIfNotNullOrUndefined(output, key, sourceValue);
        }
      }
    });

    output = await mapAdditionalFields(input, output);
    const picklistFields = [
      "canadaResidencyStatus",
      "levelOfStudy",
      "studyCompleted",
      "positionStatus",
      "emergencyContactRelationship",
      "testName",
      "standardizedAdmissionsTest",
    ];
    const countryCodePicklistFields = [
      "country",
      "permanentCountry",
      "citizenship",
    ];
    const transformedOutput = await transformPicklistFields(
      output,
      picklistFields,
      countryCodePicklistFields,
      picklistMappings
    );
    return transformedOutput;
  } catch (error) {
    throw error;
  }
}
async function mapAdditionalFields(input: any, output: any) {
  const additionalFields = {
    brand: "UCW",
    leadSource: "Student",
    businessUnit: "a0g0O00000JkvQJQAZ",
    businessUnitFilter: "UCW",
    stage: "Documents Stage",
    admissionStage: input.isSubmitted
      ? "Application submitted"
      : "Draft Application",
    applicationStatus: "submitted",
    applicationSource: "UCW",
    applicationFilledBy: "student",
    opportunityApplicationSource: "Portal uLink",
    sectionLabel: "REVIEW_AND_SUBMIT",
    progress: 100,
    isSubmitted: true,
  };

  Object.entries(additionalFields).forEach(([key, value]) => {
    setIfNotNullOrUndefined(output, key, value);
  });
  return output;
}

function getValue(source: any, path: string | string[]) {
  if (Array.isArray(path)) {
    return path.map((p) => getValue(source, p)).filter(Boolean);
  } else {
    const objectKeys = Object.keys(source);

    for (const objKey of objectKeys) {
      if (path.startsWith(objKey + ".")) {
        const nestedPath = path.substring(objKey.length + 1);
        return resolveNestedValue(source[objKey], nestedPath);
      }
    }

    return resolveNestedValue(source, path);
  }
}

function resolveNestedValue(obj: any, path: string) {
  const keys = path.split(".");
  return keys.reduce(
    (obj, key) => (obj && obj[key] !== undefined ? obj[key] : undefined),
    obj
  );
}

async function transformPicklistFields(
  output: any | any[],
  picklistFields: string[],
  countryCodePicklistFields: string[],
  picklistMappings: any
) {
  try {
    function applyMapping(fieldValue: any, mapping: any) {
      return mapping[fieldValue] || fieldValue;
    }

    function transformItem(item: any) {
      picklistFields.forEach((field) => {
        if (item[field] && picklistMappings[field]) {
          item[field] = applyMapping(item[field], picklistMappings[field]);
        }
      });

      countryCodePicklistFields.forEach((field) => {
        if (item[field] && picklistMappings.countryCode) {
          item[field] = applyMapping(item[field], picklistMappings.countryCode);
        }
      });

      if (Array.isArray(item.institutions)) {
        item.institutions.forEach(transformItem);
      }
      if (Array.isArray(item.priorEmployments)) {
        item.priorEmployments.forEach(transformItem);
      }
    }

    transformItem(output);

    return output;
  } catch (error) {
    throw error;
  }
}

const readCSVFromS3 = async (key: string): Promise<string[]> => {
  const params = {
    Bucket: BUCKET_NAME,
    Key: `${FOLDER_NAME}/${key}`,
  };

  console.log("CSV params ->", params);
  const csvData: string[] = [];
  const s3Stream = s3.getObject(params).createReadStream();

  return new Promise((resolve, reject) => {
    s3Stream
      .pipe(csvParser())
      .on("data", (row) => {
        const applicationId = row["ApplicationId"];
        if (applicationId) {
          csvData.push(applicationId);
        }
      })
      .on("end", () => resolve(csvData))
      .on("error", (error) => reject(error));
  });
};

// const getOpportunityIdFromS3 = async (key: string, applicationId: string): Promise<string | null> => {
//     const params = {
//         Bucket: BUCKET_NAME,
//         Key: `${FOLDER_NAME}/${key}`,
//     };

//     console.log("CSV params ->", params);

//     const s3Stream = s3.getObject(params).createReadStream();

//     return new Promise((resolve, reject) => {
//         let opportunityId: string | null = null;

//         s3Stream
//             .pipe(csvParser())
//             .on('data', (row) => {
//                 if (row['ApplicationId'] === applicationId) {
//                     opportunityId = row['Opportunity ID'];
//                 }
//             })
//             .on('end', () => resolve(opportunityId))
//             .on('error', (error) => reject(error));
//     });
// };

const readJSONFromS3 = async (applicationId: string): Promise<any> => {
  const params = {
    Bucket: BUCKET_NAME,
    Key: `${FOLDER_NAME}/${applicationId}.json`,
  };

  console.log("JSON params ->", params);
  try {
    const data = await s3.getObject(params).promise();
    return JSON.parse(data?.Body?.toString("utf-8"));
  } catch (error) {
    console.error(
      `Error fetching JSON for Application ID: ${applicationId}`,
      error
    );
  }
};

export const handleLegacyReviewedReviewApps = async () => {
  try {
    const pendingReviewIds = await readCSVFromS3("PendingReview.csv");
    console.log("pendingReviewIds -->", pendingReviewIds);
    await processReviewApplications(pendingReviewIds, "Unassigned");

    const completedReviewIds = await readCSVFromS3("CompletedReview.csv");
    console.log("completedReviewIds -->", pendingReviewIds);
    await processReviewApplications(completedReviewIds, "Completed - Accepted");
  } catch (error) {
    console.error("Error processing legacy review apps:", error);
  }
};

const processReviewApplications = async (applicationIds, smarStatus) => {
  for (const applicationId of applicationIds) {
    const jsonData = await readJSONFromS3(applicationId);
    console.log(`jsonData for ${applicationId}`, jsonData);
    if (jsonData) {
      const requestId = uuidv4();
      try {
        const record = await dynamodbService.getObject(
          `ucw-legacy-applications-${stage}`,
          {
            PK: "UCW_LEGACY_APPS",
            SK: jsonData?.uuid,
          }
        );

        console.log("Excisting record:", JSON.stringify(record.Item));
        if (
          !record.Item ||
          (record.Item.status === "Failed" &&
            record.Item.smarStatus === smarStatus)
        ) {
          const studentDetails = await transformInputData(jsonData);
          studentDetails["requestId"] = requestId;
          studentDetails["APIKEY"] = process.env.UCW_KEY;

          console.log("studentDetails ->", studentDetails);

          await handleUcwSfSaveOrUpdateRequests(studentDetails);
          // const opportunityId = await getOpportunityIdFromS3(s3Key, applicationId)
          // console.log("opportunityId ->", opportunityId)
          // await updateOpportunityRecord(opportunityId, smarStatus)

          await dynamodbService.putObject(`ucw-legacy-applications-${stage}`, {
            PK: "UCW_LEGACY_APPS",
            SK: jsonData.uuid,
            applicationId: jsonData.uuid,
            smarStatus,
            status: "Success",
            appDetails: jsonData,
          });

          await cloudWatchLoggerService.log(
            requestId,
            new Date().toISOString(),
            loggerEnum.Component.OAP_HANDLERS,
            "UCW_LEGACY_MIGRATION_BUCKET",
            loggerEnum.Component.GUS_EIP_SERVICE,
            loggerEnum.Event.OPERATION_COMPLETED,
            "",
            { applicationId },
            studentDetails,
            `${jsonData.uuid} application processed successfully`,
            "UCW",
            jsonData?.data?.personalEmail,
            `oap-handlers/${jsonData.uuid}/${requestId}`,
            "EducationHistoryRecord_LanguageProficiency_WorkHistory_Connections_IndentityInfoRecord",
            jsonData.uuid,
            "",
            jsonData.uuid
          );
        } else {
          console.log(`Record with ${jsonData.uuid} is already processed!`);
        }
      } catch (error) {
        await dynamodbService.putObject(`ucw-legacy-applications-${stage}`, {
          PK: "UCW_LEGACY_APPS",
          SK: jsonData.uuid,
          applicationId: jsonData.uuid,
          smarStatus,
          status: "Failed",
          appDetails: jsonData,
        });

        console.error("Error ->", error);

        await cloudWatchLoggerService.error(
          requestId,
          new Date().toISOString(),
          loggerEnum.Component.OAP_HANDLERS,
          "UCW_LEGACY_APPLICATION_QUEUE",
          "OAP_SF_TOPIC",
          loggerEnum.Event.OPERATION_COMPLETED,
          "",
          { applicationId },
          jsonData,
          error.message ? error.message : JSON.stringify(error),
          "UCW",
          jsonData?.data?.personalEmail,
          `oap-handlers/${jsonData.uuid}/${requestId}`,
          "EducationHistoryRecord_LanguageProficiency_WorkHistory_Connections_IndentityInfoRecord",
          jsonData.uuid,
          "",
          jsonData.uuid
        );
      }
    } else {
      console.log(`No jsonData found for ${applicationId}`);
      await dynamodbService.putObject(`ucw-legacy-applications-${stage}`, {
        PK: "UCW_LEGACY_APPS",
        SK: applicationId,
        applicationId: applicationId,
        smarStatus,
        status: "No json found",
        appDetails: jsonData || {},
      });
    }
  }
};

// const updateOpportunityRecord = async (opportunityId, smarStatus) => {
//     // const opportunityResponse = await getEipData(
//     //     `gus/opportunityId/${applicationId}`,
//     //     '',
//     //     process.env.UCW_KEY
//     // )

//     // console.log(opportunityResponse)

//     const updateOpportunity = await postData(
//         `gus/updateOpportunity/${opportunityId}`,
//         {
//             Document__c: smarStatus
//         },
//         process.env.UCW_KEY
//     )

//     console.log(updateOpportunity)
// }
