import * as AWS from "aws-sdk";

const s3 = new AWS.S3({ region: "eu-west-1" });
const dynamoDB = new AWS.DynamoDB.DocumentClient({ region: "eu-west-1" });

const MAX_CONCURRENCY = 50;
const FAILED_RECORDS_TABLE = "metadata-modifier-failed-records";

const processTask = async (task: any) => {
    const { s3Bucket: bucketName, s3Key: objectKey } = task;
    const decodedKey = decodeURIComponent(objectKey.replace(/\+/g, " "));

    console.log("Processing object:", decodedKey);

    try {
        const processingKey = `processing-files/${decodedKey}`;
        await s3.copyObject({
            Bucket: bucketName,
            Key: processingKey,
            CopySource: `${bucketName}/${encodeURIComponent(decodedKey)}`,
            MetadataDirective: "COPY",
        }).promise();
        console.log(`File copied to processing folder: ${processingKey}`);

        const getContentType = (fileName: string): string => {
            const extension = fileName.split('.').pop()?.toLowerCase();
            const mimeTypes: { [key: string]: string } = {
                "pdf": "application/pdf",
                "doc": "application/msword",
                "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "xls": "application/vnd.ms-excel",
                "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "ppt": "application/vnd.ms-powerpoint",
                "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                "txt": "text/plain",
                "csv": "text/csv",
                "rtf": "application/rtf",
                "jpg": "image/jpeg",
                "jpeg": "image/jpeg",
                "png": "image/png",
                "gif": "image/gif",
                "bmp": "image/bmp",
                "tiff": "image/tiff",
                "tif": "image/tiff",
                "webp": "image/webp",
                "svg": "image/svg+xml",
                "mp3": "audio/mpeg",
                "wav": "audio/wav",
                "ogg": "audio/ogg",
                "aac": "audio/aac",
                "flac": "audio/flac",
                "m4a": "audio/mp4",
                "mp4": "video/mp4",
                "mov": "video/quicktime",
                "avi": "video/x-msvideo",
                "wmv": "video/x-ms-wmv",
                "flv": "video/x-flv",
                "mkv": "video/x-matroska",
                "webm": "video/webm",
                "zip": "application/zip",
                "rar": "application/vnd.rar",
                "tar": "application/x-tar",
                "gz": "application/gzip",
                "7z": "application/x-7z-compressed",
                "html": "text/html",
                "htm": "text/html",
                "css": "text/css",
                "js": "application/javascript",
                "json": "application/json",
                "xml": "application/xml",
                "yaml": "text/yaml",
                "yml": "text/yaml",
                "woff": "font/woff",
                "woff2": "font/woff2",
                "ttf": "font/ttf",
                "otf": "font/otf",
                "apk": "application/vnd.android.package-archive",
                "exe": "application/octet-stream",
                "dmg": "application/octet-stream",
                "iso": "application/x-iso9660-image"
            };

            return extension && mimeTypes[extension] ? mimeTypes[extension] : "application/octet-stream";
        };


        const contentType = getContentType(decodedKey);
        console.log("Determined Content Type:", contentType);
        await s3.copyObject({
            Bucket: bucketName,
            Key: decodedKey,
            CopySource: `${bucketName}/${encodeURIComponent(decodedKey)}`,
            MetadataDirective: "REPLACE",
            ContentType: contentType
        }).promise();
        console.log(`Metadata updated for: ${decodedKey}`);

        return { taskId: task.taskId, resultCode: "Succeeded" };

    } catch (error) {
        console.error(`Error processing ${decodedKey}:`, error);

        // Store failed record in DynamoDB
        await dynamoDB.put({
            TableName: FAILED_RECORDS_TABLE,
            Item: {
                PK: bucketName,
                SK: decodedKey,
                errorMessage: error?.message ? error.message : JSON.stringify(error),
                status: "Failed",
                createdDate: new Date().toISOString(),
            }
        }).promise();

        return { taskId: task.taskId, resultCode: "PermanentFailure", errorMessage: error.message };
    }
};

const chunkTasks = (tasks: any, chunkSize: number) => {
    const result = [];
    for (let i = 0; i < tasks.length; i += chunkSize) {
        result.push(tasks.slice(i, i + chunkSize));
    }
    return result;
};

export const handles3MetadataModifier = async (event: any) => {
    console.log(JSON.stringify(event))
    const results = [];
    const taskChunks = chunkTasks(event.tasks, MAX_CONCURRENCY);

    for (const chunk of taskChunks) {
        const chunkResults = await Promise.all(chunk.map(processTask));
        results.push(...chunkResults);
    }

    console.log("Results:", results);
    return { results, ...event };
};
