import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  // salesforceStudentConfig,
  // salesforceStudentSubObjectsConfig
} from "src/functions/lsbfmyr-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleLsbfmyrSfSaveOrUpdateRequests = async (event) => {
  console.log(JSON.stringify(event));
  const brand = "LSBFMYR";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    console.log("App details -->", applicationDetails);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let referenceMappedDetails;
    console.log(
      "Modified Application details document -->",
      applicationDetails.documents
    );
    try {
      if (
        !applicationDetails?.isPermanentAddressDiffer ||
        applicationDetails?.isPermanentAddressDiffer === "No"
      ) {
        applicationDetails = {
          ...applicationDetails,
          permanentStreetAddress: applicationDetails?.streetAddress,
          permanentCity: applicationDetails?.city,
          permanentPostalCode: applicationDetails?.postalCode,
          permanentCountry: applicationDetails?.country,
          permanentCountryDisplayName: applicationDetails?.countryDisplayName,
        };
      }
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        applicationDetails.identityDocumentObject = [
          {
            identityNumber: applicationDetails.passportNumber,
            identityType: "Passport",
          },
        ];
        const mappings = {
          institutions: "educationHistory",
        };
        for (const [sourceKey, targetKey] of Object.entries(mappings)) {
          if (applicationDetails.hasOwnProperty(sourceKey)) {
            applicationDetails[targetKey] = applicationDetails[sourceKey];
          }
        }
        if (applicationDetails?.institutions[0]?.institutionName) {
          applicationDetails.leadInstitutionName =
            applicationDetails?.institutions[0]?.institutionName;
        }
        if (applicationDetails?.educationHistory) {
          let initialCount = 1;
          applicationDetails?.educationHistory.forEach((institutions) => {
            institutions["institutionOrder"] = initialCount;
            initialCount++;
          });
        }

        applicationDetails = {
          ...applicationDetails,
          ...buildMappings(applicationDetails),
        };
        console.log("applicationDetails", applicationDetails);

        referenceMappedDetails = await opportunityFileReferenceMapping(
          applicationDetails
        );
        applicationDetails = referenceMappedDetails.appDetails;
      }
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      if (
        referenceMappedDetails?.institutionData ||
        referenceMappedDetails?.testData
      ) {
        const mappedData = await Promise.all([
          referenceMappedDetails?.institutionData
            ? mapSalesforceObject(
                referenceMappedDetails.institutionData,
                filledBy
              )
            : null,
          referenceMappedDetails?.testData
            ? mapSalesforceObject(referenceMappedDetails.testData, filledBy)
            : null,
          mapSalesforceObject(applicationDetails, filledBy),
        ]);

        const [mappedInstitutionData, mappedTestData, mappedRequest] =
          mappedData;
        console.log("Request", request);
        await Promise.all([
          mappedInstitutionData &&
            postDataSf(
              { ...request, ...mappedInstitutionData },
              APIKEY,
              filledBy
            ),
          mappedTestData &&
            postDataSf({ ...request, ...mappedTestData }, APIKEY, filledBy),
          postDataSf({ ...request, ...mappedRequest }, APIKEY, filledBy),
        ]);
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        console.log("Request", request);
        await postDataSf(request, APIKEY, filledBy);
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue) => {
      documentIds.forEach((docId) => {
        const matchingDocument = documentMap.get(docId);
        if (matchingDocument) {
          matchingDocument[referenceKey] = referenceValue;
        }
      });
    };
    let institutionData = {
      documents: [],
      educationHistory: [],
    };

    let testData = {
      documents: [],
      languageProficiency: [],
    };

    if (Array.isArray(appDetails.institutions)) {
      appDetails.institutions.forEach((institution, index) => {
        const institutionOrder = index + 1;
        const testCertificates = institution.testCertificateId || [];

        const documentIds = [...testCertificates.map((doc) => doc.documentId)];

        addReferences(
          documentIds,
          "eduReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );

        institutionData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        institutionData.educationHistory.push(institution);
      });

      appDetails.educationHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !institutionData.documents.some(
            (institutionDoc) => institutionDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }

    if (Array.isArray(appDetails.languageProficiency)) {
      appDetails.languageProficiency.forEach((test, index) => {
        const testOrder = index + 1;
        const testCertificateId = test.report || [];

        const documentIds = [...testCertificateId.map((doc) => doc.documentId)];

        addReferences(
          documentIds,
          "testReference",
          `@{LanguageProficiencyRecord__c_${testOrder}.id}`
        );

        testData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        testData.languageProficiency.push(test);
      });
      appDetails.languageProficiency = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !testData.documents.some(
            (testDoc) => testDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("Invalid or missing appDetails.documents.");
    }
    const categories = [
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
    ];

    categories.forEach(({ key, referenceKey, referenceValue }) => {
      if (Array.isArray(appDetails[key])) {
        const documentIds = appDetails[key].map((doc) => doc.documentId);
        addReferences(documentIds, referenceKey, referenceValue);
      }
    });

    console.log("Processed appDetails.documents:", appDetails.documents);
    console.log("Institution data moved:", institutionData);
    console.log("TestData moved:", testData);

    return { appDetails, institutionData, testData };
  }
  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
    testData: { documents: [], languageProficiency: [] },
  };
}

function buildMappings(applicationDetails) {
  try {
    const {
      guardianName,
      emergencyContactMobilePhone,
      emergencyContactEmail,
      emergencyResidance,
      testName,
      testDate,
      overallScore,
      report,
    } = applicationDetails;

    let mappings = {
      connections: [
        {
          guardianName,
          emergencyContactMobilePhone,
          emergencyContactEmail,
          emergencyResidance,
          connectionType: "Emergency",
        },
      ],
      languageProficiency: [
        {
          name: testName,
          testDate,
          overallScore,
          report,
        },
      ],
    };

    return mappings;
  } catch (error) {
    throw error;
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig =
    filledBy === "agent" ? salesforceAgentConfig : salesforceAgentConfig;
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceAgentConfig;
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      if (applicationDetails[salesforceConfig[object][0]]) {
        for (const record of applicationDetails[salesforceConfig[object][0]]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[salesforceConfig[object][0]]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    }
  }

  return result;
}
