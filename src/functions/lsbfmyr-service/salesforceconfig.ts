export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  educationHistory: {
    highestQualifications: "Degree_earned__c",
    institutionName: "InstitutionName__c",
    yearOfCompletion: "GraduationDate__c",
  },
  connections: {
    guardianName: "Name",
    "emergencyContactMobilePhone.numberWithCode": "Mobile_c__c",
    emergencyContactEmail: "Email_c__c",
    emergencyResidance: "Address_c__c",
  },
  languageProficiency: {
    name: "ProficiencyQualification__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityType: "Identity_Type__c",
  },
};

export const salesforceAgentConfig = {
  EducationHistoryRecord__c: ["educationHistory"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  OpportunityTeamMember: ["teamMembers"],
  Connection__c: ["connections"],
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    countryDisplayName: ["Country__c", "PersonMailingCountry"],
    gender: "Gender__c",
    // name: "Name",
    passportNumber: "Passport__pc",
    correspondenceCity: "ShippingCity",
    correspondencePostalCode: "ShippingPostalCode",
    correspondenceCountryDisplayName: "ShippingCountry",
    correspondenceStateDisplayName: "ShippingState",
    correspondenceAddress: "ShippingStreet",
    // street: "PersonMailingAddress",
    city: "gaconnector_City__c",
    postalCode: "PersonMailingPostalCode",
    mailingStateDisplayName: "PersonMailingState",
    residance: "PersonMailingStreet",
    "phoneNumber.numberWithCode": ["Mobile__c", "Phone"],
    accountRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    birthDate: "DateofBirth__c",
    maritalStatus: "Marital_Status__c",
    agentContactUserId: "OwnerId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
  },
  Lead: {
    firstName: "FirstName",
    leadSource: "LeadSource",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    countryDisplayName: ["Country"],
    email: "Email",
    brand: "Brand__c",
    program: "Programme__c",
    lastName: "LastName",
    gender: "Gender__c",
    name: "Name",
    "phoneNumber.numberWithCode": ["Phone", "MobilePhone"],
    correspondenceCountryDisplayName: "Country__c",
    agentContactUserId: "OwnerId",
    //country: "Country__c"
  },
  Application__c: {
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    firstName: "First_Name__c",
    intake: "Intake__c",
    program: "Programme__c",
    gender: "Gender__c",
    name: "Name",
    passportNumber: "Passport_Number__c",
    correspondenceAddress: "Street_Address__c",
    correspondenceCity: "City__c",
    correspondencePostalCode: "Postcode__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    birthDate: "Date_of_birth__c",
    applicationRecordTypeId: "RecordTypeId",
    fullName: "Name",
    email: "Email__c",
    programDisplayName: "Program_Of_Study__c",
    countryDisplayName: ["Country__c", "Country_of_Birth__c"],
  },
  // Contact: {
  //     passportNumber: "Passport__c"
  // },
  Opportunity: {
    appId: "ApplicationId__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    program: "Programme__c",
    correspondenceCity: "gaconnector_City__c",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    isAcceptedTermsAndConditions: "DeclarationInfoProvided__c",
    stage: "StageName",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    // correspondenceCountry: "gaconnector_City__c",
    opportunityApplicationSource: "ApplicationSource__c",
    "phoneNumber.numberWithCode": "AccountPhone__c",
    pricebookId: "Pricebook2Id",
    progress: "ApplicationProgress__c",
    admissionStage: "AdmissionsStage__c",
    isSubmitted: "ApplicationSubmitted__c",
    email: "Email__c",
    submittedDate: "DeclarationDate__c",
    submissionSignature: "DeclarationSignature__c",
  },
  ProofOfIdentity: {
    passportNumber: "Identity_Document_Number__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};

//OpportunityLineItem, PersonMailingadd, Lead -> gender, Name (sorted)
