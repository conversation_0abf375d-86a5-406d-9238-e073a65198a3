
import { handleHzuDeferral } from './changeRequest/deferral';

export const handleChangeRequest = async (records: any[]) => {
  if (!records || !Array.isArray(records) || records.length === 0) {
    console.log('No records to process or records is not an array');
    return;
  }

  for (const record of records) {
    try {
      console.log('Processing record:', record.messageId);
      
      const eventBody = JSON.parse(record.body);
      const payload = eventBody.payload;
      
      // Extract scenario from payload
      const scenario = payload.Scenario__c;
      console.log(`Processing change request with scenario: ${scenario}`);

      // Route to appropriate handler based on scenario
      switch (scenario.toUpperCase()) {
        case 'DEFERRAL_REQUEST':
          await handleHzuDeferral(record);
          break;
        // Add more scenarios as needed
        // case 'BIODATA_CHANGE':
        //   await handleBiodataChange(record);
        //   break;
        // case 'WITHDRAWAL':
        //   await handleWithdrawal(record);
        //   break;
        default:
          console.log(`Unhandled scenario: ${scenario}`);
      }
    } catch (error) {
      console.error('Error processing change request:', error);
    }
  }
};

export const hzuChangeRequests: any = async event => {
  console.log('Event -->', JSON.stringify(event));
  await handleChangeRequest(event?.Records);
};

export const handleHzuChangeRequests = hzuChangeRequests;

