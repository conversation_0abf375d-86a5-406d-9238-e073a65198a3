export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    institutionName: "InstitutionName__c",
    institutionType: "Institution_Type__c",
    institutionGPA: "GPA_Score__c",
    institutionStartDate: "EnrolmentDateYear__c",
    institutionEndDate: "GraduationDate__c",
    earnedDegreeDisplayName: "Degree_earned__c",
    canShareTransferringCredits: "Transferring_credits_to_our_Institution__c",
    countryDisplayName: "Address__c",
  },
  workHistory: {
    employerName: "Employer__c",
    employerPosition: "Position__c",
    employmentStartDate: "StartDate__c",
    "employmentWorkPhone.numberWithCode": "Work_Phone__c",
  },
  languageProficiency: {
    testNameDisplayName: "ProficiencyQualification__c",
    testReportFormNo: "Test_link_Certification__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactLastName: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    connectionType: "Type__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityType: "Identity_Type__c",
  },
};

export const salesforceAgentConfig = {
  Survey_AP__c: {
    countryDisplayName: "Country__c",
    email: "Email_Address__c",
    proficiencyTestScore: "English_Proficiency_Test_Scores__c",
    backlogInDegree: "Have_20_Backlogs_in__c",
    overallCGPA: "Have_CGPA_at_2_0__c",
    overStayDuringVisa: "Have_you_overstayed__c",
    fundingForStudies: "How_will_you_be_funding_your_studies__c",
    businessUnitFilter: "Institution__c",
    fullName: "Name__c",
    "phoneNumber.numberWithCode": "Phone__c",
    isStudentVisaRejected: "Visa_Rejection_Last_6_Months__c",
    surveyIsQualified: "Qualified__c",
    isStudentVisaRejected_isQualified: "Q1_Qualification__c",
    backlogInDegree_isQualified: "Q2_Qualification__c",
    overallCGPA_isQualified: "Q3_Qualification__c",
    proficiencyTestScore_isQualified: "Q4_Qualification__c",
    fundingForStudies_isQualified: "Q5_Qualification__c",
    overStayDuringVisa_isQualified: "Q6_Qualification__c",
    surveyRecordTypeId: "RecordTypeId",
  },
  OpportunityTeamMember: ["teamMembers"],
  EducationHistoryRecord__c: ["educationHistory"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  WorkHistoryRecord__c: ["workHistory"],
  Connection__c: ["connections"],
  Lead: {
    haveWorkExperience: "WorkExperience__c",
    hearAboutUs: "How_did_you_hear_about_us__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    countryDisplayName: ["Country", "Country__c"],
    program: "Programme__c",
    firstName: "FirstName",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    lastName: "LastName",
    location: "Location__c",
    "phoneNumber.numberWithCode": "Phone",
    // gender: "Gender__c", commenting gender because there is no relevant picklist value in salesforce for 'Prefer not to Disclose'
    "otherNumber.numberWithCode": "OtherPhone__c",
    email: "Email",
    visa: "applicationinfo_visa__c",
    preferredContactOption: "PreferredContactMethod__c",
    educationalInstitutionName: "Name_of_the_institution__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    passportNumber: "Passport__pc",
    firstName: "FirstName",
    lastName: "LastName",
    middleName: "Middle_Name__c",
    email: "PersonEmail",
    "phoneNumber.numberWithCode": ["Mobile__c", "Phone"],
    countryDisplayName: "Country__c",
    formerLastName: "Former_Last_Name__c",
    birthDate: "DateofBirth__c",
    gender: "Gender__c",
    "otherNumber.numberWithCode": "PersonOtherPhone",
    maritalStatusDisplayName: "Marital_Status__c",
    preExistingCondition: "Health_Information__c",
    street: "PersonMailingStreet",
    city: "gaconnector_City__c",
    mailingCity: "ShippingCity",
    mailingCountryDisplayName: "ShippingCountry",
    mailingPostCode: "ShippingPostalCode",
    mailingStateDisplayName: "ShippingState",
    mailingStreet: "ShippingStreet",
    postalCode: "PersonMailingPostalCode",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
    preferredName: "PreferedFirstName__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
    placeOfBirth: "PlaceOfBirth__c",
    currentLocationDisplayName: "Current_Location__c",
    originCountryDisplayName: "Country_of_Origin__c",
  },
  Opportunity: {
    miscDetails: "Application_Misc_Details__c",
    visaType: "VisaComments__c",
    appId: "ApplicationId__c",
    isEmployerOfferTuition: "Tuition_Reimbursement_by_Employer__c",
    location: "Location__c",
    city: "gaconnector_City__c",
    mailingStateDisplayName: "State__c",
    visa: "visa__c",
    isCurrentEmployee: "Are_you_currently_employed_with_us__c",
    agreeToUniversityDisclosure: "DeclarationInfoProvided__c",
    submissionSignature: "DeclarationSignature__c",
    // highSchoolCompletionStatus: "highestqualification__c",
    submittedDate: "DeclarationDate__c",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    program: "Programme__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    haveTrafficViolation: "CriminalConvictions__c",
    admissionStage: "AdmissionsStage__c",
    documentStatus: "Document__c",
    assignedTo: "Assigned_To__c",
  },
  Application__c: {
    applicationId: "Application_Form_Id__c",
    isFirstGenerationCollegeStudent: "First_Generation_College_Student__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    firstName: "First_Name__c",
    lastName: "Last_Name__c",
    email: "Email__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    countryDisplayName: "Country__c",
    programTypeDisplayName: "Level__c",
    middleName: "Middle_Other_Name_s__c",
    birthDate: "Date_of_birth__c",
    gender: "Gender__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
    street: "Street_Address__c",
    city: "City__c",
    postCode: "Postcode__c",
    emergencyContactFirstName: "First_Name_Emergency__c",
    emergencyContactLastName: "Surname_Emergency__c",
    "emergencyContactPhoneNumber.numberWithCode": "Emergency_Phone_Number__c",
    emergencyContactEmail: "Emergency_Email__c",
    educationalInstitutionName: "Institution_Name_Proficiency__c",
    intake: ["Start_Date__c", "Intake__c"],
    employerName: "Employer_1__c",
    employerPosition: "Position_Title_2__c",
    employmentStartDate: "Employment_Start_Date_1__c",
    fullName: "Name",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    programDisplayName: "Program_Of_Study__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};

export const salesforceStudentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    institutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    institutionName: "InstitutionName__c",
    institutionType: "Institution_Type__c",
    institutionGPA: "GPA_Score__c",
    institutionStartDate: "EnrolmentDateYear__c",
    institutionEndDate: "GraduationDate__c",
    earnedDegreeDisplayName: "Degree_earned__c",
    canShareTransferringCredits: "Transferring_credits_to_our_Institution__c",
    countryDisplayName: "Address__c",
  },
  workHistory: {
    employerName: "Employer__c",
    employerPosition: "Position__c",
    employmentStartDate: "StartDate__c",
    "employmentWorkPhone.numberWithCode": "Work_Phone__c",
  },
  languageProficiency: {
    testNameDisplayName: "ProficiencyQualification__c",
    testReportFormNo: "Test_link_Certification__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactLastName: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    connectionType: "Type__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityType: "Identity_Type__c",
  },
};

export const salesforceStudentConfig = {
  Survey_AP__c: {
    countryDisplayName: "Country__c",
    email: "Email_Address__c",
    proficiencyTestScore: "English_Proficiency_Test_Scores__c",
    backlogInDegree: "Have_20_Backlogs_in__c",
    overallCGPA: "Have_CGPA_at_2_0__c",
    overStayDuringVisa: "Have_you_overstayed__c",
    fundingForStudies: "How_will_you_be_funding_your_studies__c",
    businessUnitFilter: "Institution__c",
    fullName: "Name__c",
    phoneNumber: "Phone__c",
    isStudentVisaRejected: "Visa_Rejection_Last_6_Months__c",
    surveyIsQualified: "Qualified__c",
    isStudentVisaRejected_isQualified: "Q1_Qualification__c",
    backlogInDegree_isQualified: "Q2_Qualification__c",
    overallCGPA_isQualified: "Q3_Qualification__c",
    proficiencyTestScore_isQualified: "Q4_Qualification__c",
    fundingForStudies_isQualified: "Q5_Qualification__c",
    overStayDuringVisa_isQualified: "Q6_Qualification__c",
    surveyRecordTypeId: "RecordTypeId",
  },
  EducationHistoryRecord__c: ["educationHistory"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  WorkHistoryRecord__c: ["workHistory"],
  Connection__c: ["connections"],
  Lead: {
    haveWorkExperience: "WorkExperience__c",
    hearAboutUs: "How_did_you_hear_about_us__c",
    leadSource: "LeadSource",
    brand: "Brand__c",
    countryDisplayName: ["Country", "Country__c"],
    opportunityApplicationSource: "ApplicationSource__c",
    program: "Programme__c",
    firstName: "FirstName",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    lastName: "LastName",
    location: "Location__c",
    gender: "Gender__c",
    "phoneNumber.numberWithCode": "Phone",
    "otherNumber.numberWithCode": "OtherPhone__c",
    email: "Email",
    visa: "applicationinfo_visa__c",
    preferredContactOption: "PreferredContactMethod__c",
    educationalInstitutionName: "Name_of_the_institution__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    passportNumber: "Passport__pc",
    firstName: "FirstName",
    lastName: "LastName",
    middleName: "Middle_Name__c",
    email: "PersonEmail",
    "phoneNumber.numberWithCode": ["Mobile__c", "Phone"],
    countryDisplayName: "Country__c",
    formerLastName: "Former_Last_Name__c",
    birthDate: "DateofBirth__c",
    gender: "Gender__c",
    "otherNumber.numberWithCode": "PersonOtherPhone",
    maritalStatusDisplayName: "Marital_Status__c",
    preExistingCondition: "Health_Information__c",
    street: "PersonMailingStreet",
    city: "gaconnector_City__c",
    mailingCity: "ShippingCity",
    mailingCountryDisplayName: "ShippingCountry",
    mailingPostCode: "ShippingPostalCode",
    mailingStateDisplayName: "ShippingState",
    mailingStreet: "ShippingStreet",
    postalCode: "PersonMailingPostalCode",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
    preferredName: "PreferedFirstName__c",
    preferredContactOption: "Preferred_Contact_Method__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
    placeOfBirth: "PlaceOfBirth__c",
    currentLocationDisplayName: "Current_Location__c",
    originCountryDisplayName: "Country_of_Origin__c",
  },
  Opportunity: {
    miscDetails: "Application_Misc_Details__c",
    visaType: "VisaComments__c",
    appId: "ApplicationId__c",
    isEmployerOfferTuition: "Tuition_Reimbursement_by_Employer__c",
    location: "Location__c",
    city: "gaconnector_City__c",
    mailingStateDisplayName: "State__c",
    visa: "visa__c",
    isCurrentEmployee: "Are_you_currently_employed_with_us__c",
    agreeToUniversityDisclosure: "DeclarationInfoProvided__c",
    submissionSignature: "DeclarationSignature__c",
    // highSchoolCompletionStatus: "highestqualification__c",
    submittedDate: "DeclarationDate__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    program: "Programme__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    pricebookId: "Pricebook2Id",
    haveTrafficViolation: "CriminalConvictions__c",
    admissionStage: "AdmissionsStage__c",
    documentStatus: "Document__c",
    assignedTo: "Assigned_To__c",
  },
  Application__c: {
    applicationId: "Application_Form_Id__c",
    isFirstGenerationCollegeStudent: "First_Generation_College_Student__c",
    firstName: "First_Name__c",
    lastName: "Last_Name__c",
    email: "Email__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    countryDisplayName: "Country__c",
    programTypeDisplayName: "Level__c",
    middleName: "Middle_Other_Name_s__c",
    birthDate: "Date_of_birth__c",
    gender: "Gender__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
    street: "Street_Address__c",
    city: "City__c",
    postCode: "Postcode__c",
    emergencyContactFirstName: "First_Name_Emergency__c",
    emergencyContactLastName: "Surname_Emergency__c",
    "emergencyContactPhoneNumber.numberWithCode": "Emergency_Phone_Number__c",
    emergencyContactEmail: "Emergency_Email__c",
    educationalInstitutionName: "Institution_Name_Proficiency__c",
    intake: ["Start_Date__c", "Intake__c"],
    employerName: "Employer_1__c",
    employerPosition: "Position_Title_2__c",
    employmentStartDate: "Employment_Start_Date_1__c",
    fullName: "Name",
    programDisplayName: "Program_Of_Study__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};
