export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityExpiryDate: "Expiry_Date__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityType: "Identity_Type__c",
  }
};
export const salesforceAgentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  OpportunityTeamMember: ["teamMembers"],
  Lead: {
    email: "Email",
    lastName: "LastName",
    countryDisplayName: "Country",
    leadSource: "LeadSource",
    brand: "Brand__c",
    firstName: "FirstName",
    "phone.numberWithCode": "Phone",
    street: "Street",
    postCode: "PostalCode",
    program: "Programme__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    DOB: "DateofBirth__c",
    nationalityDisplayName: "Nationality__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    agentContactUserId: "OwnerId",
  },
  Individual: {
    email: "Email__c",
    brand: "Brand__c",
    DOB: "BirthDate",
    lastName: "LastName",
    firstName: "FirstName",
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    DOB: "DateofBirth__c",
    nationalityDisplayName: "Nationality__c",
    city: ["PersonMailingCity", "gaconnector_City__c"],
    "phone.numberWithCode": ["Mobile__c", "Phone"],
    email: "PersonEmail",
    street: "PersonMailingStreet",
    countryDisplayName: ["Country__c", "CountryOfBirth__c"],
    country: "PersonMailingCountryCode",
    postCode: "PersonMailingPostalCode",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
    agentContactUserId: "OwnerId",
  },
  Opportunity: {
    appId: "ApplicationId__c",
    city: "gaconnector_City__c",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    programStartDate: ["CloseDate", "Product_Intake_Date__c"],
    program: "Programme__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    visaExpiryDate: "VisaExpiryDate__c",
    email: "Email__c",
    pricebookId: "Pricebook2Id",
    "phone.numberWithCode": ["AccountPhone__c", "AccountMobile__c"],
    submittedDate: "DeclarationDate__c",
    admissionStage: "AdmissionsStage__c",
  },
  Application__c: {
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    levelDisplayName: "Level__c",
    firstName: "First_Name__c",
    fullName: "Name",
    lastName: "Last_Name__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
    "phone.numberWithCode": "Mobile__c",
    email: "Email__c",
    countryDisplayName: ["Country__c", "Country_of_Birth__c"],
    applicationFilledBy: "FilledBy__c",
    postCode: "Postcode__c",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    DOB: "Date_of_birth__c",
    nationalityDisplayName: "Nationality__c",
    programStartDate: "Intake__c",
    programDisplayName: "Program_Of_Study__c",
    city: "City__c",
    street: "Street_Address__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};

export const salesforceStudentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityExpiryDate: "Expiry_Date__c",
    identityIssuingCountry: "Issuing_Country__c",
    identityType: "Identity_Type__c",
  }
};
export const salesforceStudentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  OpportunityFile__c: ["documents"],
  Lead: {
    email: "Email",
    lastName: "LastName",
    countryDisplayName: "Country",
    leadSource: "LeadSource",
    brand: "Brand__c",
    firstName: "FirstName",
    "phone.numberWithCode": "Phone",
    street: "Street",
    postCode: "PostalCode",
    program: "Programme__c",
    leadRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    DOB: "DateofBirth__c",
    nationalityDisplayName: "Nationality__c",
    businessUnitFilter: "BusinessUnitFilter__c",
  },
  Individual: {
    email: "Email__c",
    brand: "Brand__c",
    DOB: "BirthDate",
    lastName: "LastName",
    firstName: "FirstName",
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    DOB: "DateofBirth__c",
    nationalityDisplayName: "Nationality__c",
    city: ["PersonMailingCity", "gaconnector_City__c"],
    "phone.numberWithCode": ["Mobile__c", "Phone"],
    email: "PersonEmail",
    street: "PersonMailingStreet",
    countryDisplayName: ["Country__c", "CountryOfBirth__c"],
    country: "PersonMailingCountryCode",
    postCode: "PersonMailingPostalCode",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    levelDisplayName: "Level__pc",
  },
  Opportunity: {
    appId: "ApplicationId__c",
    city: "gaconnector_City__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    applicationId: "ApplicationFormId__c",
    programDisplayName: "Name",
    stage: "StageName",
    programStartDate: ["CloseDate", "Product_Intake_Date__c"],
    program: "Programme__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    visaExpiryDate: "VisaExpiryDate__c",
    email: "Email__c",
    pricebookId: "Pricebook2Id",
    "phone.numberWithCode": ["AccountPhone__c", "AccountMobile__c"],
    submittedDate: "DeclarationDate__c",
    admissionStage: "AdmissionsStage__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};
