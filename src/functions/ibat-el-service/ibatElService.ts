import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "src/functions/ibat-el-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);

export const handleIbatSfSaveOrUpdateRequests = async (event) => {
  const loggerEnum = new LoggerEnum();
  const brand = "IBAT";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    const filledBy = applicationDetails?.applicationFilledBy;
    const salesforceConfig =
      filledBy === "agent" ? salesforceAgentConfig : salesforceStudentConfig;
    const requestId = applicationDetails.requestId;
    const currentUTC = new Date().toISOString();
    const APIKEY = applicationDetails.APIKEY;

    try {
      applicationDetails = flattenObject(applicationDetails);
      let request = {};
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        applicationDetails.identityDocumentObject = [
          {
            identityNumber: applicationDetails.passportNumber,
            identityExpiryDate: applicationDetails.passportExpiryDate,
            identityIssuingCountry:
              applicationDetails.passportIssuingCountryDisplayName,
            identityType: "Passport",
          },
        ];
      }

      request = {
        ...request,
        ...(await mapSalesforceField(
          applicationDetails,
          salesforceConfig,
          filledBy
        )),
      };
      await postDataSf(request, APIKEY, filledBy);
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
    2;
  }
};
export const mapSalesforceField = async (
  event: Record<string, any>,
  salesforceConfig: Record<string, any>,
  filledBy: string
): Promise<Record<string, any>> => {
  let mappedObject: Record<string, any> = {};
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;
  try {
    for (const key in event) {
      if (Array.isArray(event[key])) {
        const subObject = await mapSubObject(
          key,
          event[key],
          filledBy,
          salesforceConfig,
          salesforceSubObjectsConfig
        );
        mappedObject = { ...mappedObject, ...subObject };
      }

      for (const object in salesforceConfig) {
        const mapping = salesforceConfig[object];

        if (mapping.hasOwnProperty(key)) {
          if (!mappedObject.hasOwnProperty(object)) {
            mappedObject[object] = {};
          }

          const mappedKey = Array.isArray(mapping[key])
            ? mapping[key]
            : [mapping[key]];

          for (const field of mappedKey) {
            mappedObject[object][field] = event[key];
          }
        }
      }
    }

    return mappedObject;
  } catch (error) {
    throw error;
  }
};
async function mapSubObject(
  key: string,
  objects: any[],
  filledBy: string,
  salesforceConfig: Record<string, any>,
  salesforceSubObjectsConfig: Record<string, any>
): Promise<any> {
  try {
    const response: any[] = [];

    for (const object of objects) {
      const subObject = await mapSalesforceField(
        object,
        salesforceSubObjectsConfig,
        filledBy
      );
      response.push(subObject?.[key]);
    }

    for (const object in salesforceConfig) {
      const mapping = salesforceConfig[object];
      if (Array.isArray(mapping) && mapping.includes(key)) {
        return {
          [object]: response,
        };
      }
    }
    return response;
  } catch (error) {
    throw error;
  }
}
function flattenObject(obj, parentKey = "") {
  try {
    return Object.keys(obj).reduce((acc, key) => {
      const prefixedKey = parentKey ? `${parentKey}.${key}` : key;

      if (
        typeof obj[key] === "object" &&
        obj[key] !== null &&
        !Array.isArray(obj[key])
      ) {
        // Recursively flatten only if the value is an object and not an array
        Object.assign(acc, flattenObject(obj[key], prefixedKey));
      } else {
        acc[prefixedKey] = obj[key];
      }

      return acc;
    }, {});
  } catch (error) {
    throw error;
  }
}
