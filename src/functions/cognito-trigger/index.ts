import { handlerPath } from "@libs/handler-resolver";

export const cognitoCustomEmailTrigger = {
  handler: `${handlerPath(
    __dirname
  )}/cognitoTriggerHandler.handleStudentOAPCustomEmail`,
  name: "gus-student-oap-customEmailTrigger-${self:provider.stage}",
  timeout: 60,
};

export const cognitoPreSignUpTrigger = {
  handler: `${handlerPath(__dirname)}/cognitoTriggerHandler.handlePreSignUp`,
  name: "gus-student-oap-preSignUpTrigger-${self:provider.stage}",
  timeout: 60,
};

export const cognitoPreAuthenticationTrigger = {
  handler: `${handlerPath(
    __dirname
  )}/cognitoTriggerHandler.handlePreAuthentication`,
  name: "gus-student-oap-preAuthenticationTrigger-${self:provider.stage}",
  timeout: 60,
};

export const cognitoPostConfirmationTrigger = {
  handler: `${handlerPath(
    __dirname
  )}/cognitoTriggerHandler.handlePostConfirmation`,
  name: "gus-student-oap-postConfirmationTrigger-${self:provider.stage}",
  timeout: 60,
};
