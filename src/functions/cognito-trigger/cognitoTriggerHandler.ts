import {
  preSignUp,
  studentOAPCustomEmail,
  preAuthentication,
  postConfirmation,
} from "@functions/cognito-trigger/cognitoTriggerService";

// Export individual handlers for each trigger
export const handleStudentOAPCustomEmail: any = async (event, context) => {
  await studentOAPCustomEmail(event, context);
};

export const handlePreSignUp: any = async (event, context) => {
  await preSignUp(event, context);
};

export const handlePreAuthentication: any = async (event, context) => {
  await preAuthentication(event, context);
};

export const handlePostConfirmation: any = async (event, context) => {
  await postConfirmation(event, context);
};
