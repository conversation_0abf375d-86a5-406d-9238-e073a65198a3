import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "@functions/ueg-service/salesforceconfig";
import { postDataSf } from "src/connectors/salesforce-connectors";
import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleUegSfSaveOrUpdateRequests = async (event) => {
  console.log(JSON.stringify(event));
  const brand = "UEG";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let referenceMappedDetails;
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
    }
    console.log(
      "Modified Application details document -->",
      applicationDetails.documents
    );
    try {
      if (
        applicationDetails?.applicationStatus === "submitted" &&
        !applicationDetails?.submittedToSf
      ) {
        try {
          applicationDetails.visaApplication = [
            {
              visaRequired:
                applicationDetails?.visaRequirement === "Yes" ? true : false,
            },
          ];
          applicationDetails.identityDocumentObject = [
            {
              identityNumber: applicationDetails?.passportNumber,
              identityIssueDate: applicationDetails?.passportIssueDate,
              identityExpiryDate: applicationDetails?.passportExpiryDate,
              identityIssuingCountry:
                applicationDetails?.passportIssuingCountryDisplayName,
              identityType: "Passport",
            },
          ];
          const mappings = {
            educationInfo: "educationHistory",
            priorEmployments: "workHistory",
          };
          for (const [sourceKey, targetKey] of Object.entries(mappings)) {
            if (applicationDetails.hasOwnProperty(sourceKey)) {
              applicationDetails[targetKey] = applicationDetails[sourceKey];
            }
          }
          if (applicationDetails?.educationInfo?.[0]?.institutionName) {
            applicationDetails.leadInstitutionName =
              applicationDetails?.educationInfo?.[0]?.institutionName;
          }
          console.log(
            "Education History <-",
            applicationDetails.educationHistory
          );
          if (!applicationDetails.educationHistory) {
            applicationDetails.educationHistory = [];
          }
          console.log(
            "Education History -> ",
            applicationDetails.educationHistory
          );
          if (applicationDetails.eqheDetails) {
            applicationDetails.eqheDetails?.forEach((institution) => {
              applicationDetails.educationHistory?.push({
                ...institution,
                dateOfEQHE: institution?.dateOfEQHE?.split("-")[0] || "",
                institutionType: "EQHE",
              });
            });
          }
          console.log(
            "Education History after eqheDetails ",
            applicationDetails.educationHistory
          );
          console.log(
            "Semester of initial registration",
            applicationDetails?.semesterOfInitialRegistration?.split("-")[0]
          );
          if (applicationDetails?.countryOfInitialRegistration) {
            applicationDetails.educationHistory.push({
              countryOfInitialRegistrationDisplayName:
                applicationDetails?.countryOfInitialRegistrationDisplayName,
              semesterOfInitialRegistration:
                applicationDetails?.semesterOfInitialRegistration?.split(
                  "-"
                )[0] || "",
              universityOfInitialRegistration:
                applicationDetails?.universityOfInitialRegistration,
            });
          }

          console.log(
            "Education History after initial reg ",
            applicationDetails.educationHistory
          );
          if (applicationDetails?.educationHistory) {
            let initialCount = 1;
            applicationDetails?.educationHistory.forEach((institutions) => {
              institutions["institutionOrder"] = initialCount;
              initialCount++;
            });
          }
          console.log(
            "Education History after modific",
            applicationDetails.educationHistory
          );

          referenceMappedDetails = await opportunityFileReferenceMapping(
            applicationDetails
          );

          applicationDetails?.documents.forEach((doc) => {
            if (doc.documentType === "ID/Passport") {
              doc.isIdentityInformationAccurate =
                applicationDetails.isIdentityInformationAccurate === "Yes";
            }
          });

          applicationDetails = referenceMappedDetails.appDetails;
          applicationDetails = {
            ...applicationDetails,
            ...buildMappings(applicationDetails),
          };
        } catch (error) {
          console.log("Error in mapping application details", error);
          throw error;
        }
      }

      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      if (referenceMappedDetails?.institutionData) {
        const mappedInstitutionData = {
          ...request,
          ...(await mapSalesforceObject(
            referenceMappedDetails?.institutionData,
            filledBy
          )),
        };
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        await Promise.all([
          postDataSf(mappedInstitutionData, APIKEY, filledBy),
          postDataSf(request, APIKEY, filledBy),
        ]);
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };
        await postDataSf(request, APIKEY, filledBy);
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue) => {
      if (Array.isArray(documentIds)) {
        documentIds.forEach((docId) => {
          const matchingDocument = documentMap.get(docId);
          if (matchingDocument) {
            matchingDocument[referenceKey] = referenceValue;
          }
        });
      }
    };

    const categories = [
      {
        key: "testEvidence",
        referenceKey: "testReference",
        referenceValue: "@{LanguageProficiencyRecord__c_1.id}",
      },
    ];

    if (Array.isArray(categories)) {
      categories.forEach(({ key, referenceKey, referenceValue }) => {
        if (Array.isArray(appDetails[key])) {
          const documentIds = appDetails[key].map((doc) => doc.documentId);
          addReferences(documentIds, referenceKey, referenceValue);
        }
      });
    }

    console.log("Processed appDetails.documents:", appDetails.documents);

    return { appDetails };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return { appDetails };
}

function buildMappings(applicationDetails) {
  try {
    const {
      testName,
      testDate,
      overallScore,
      testNameDisplayName,
      isEnglishFirstLanguage,
      dateOfGraduation,
      otherTestName,
      testVerificationLink,
      listeningScore,
      readingScore,
      speakingScore,
      writingScore,
    } = applicationDetails;

    let mappings = {
      languageProficiency: [
        {
          proficiencyQualification:
            isEnglishFirstLanguage === "Yes"
              ? "Native English Speaker"
              : testName,
          provider: testNameDisplayName,
          testDate,
          dateOfGraduation,
          overallScore,
          otherTestName,
          testVerificationLink,
          listeningScore,
          readingScore,
          speakingScore,
          writingScore,
        },
      ],

      miscDetails: JSON.stringify({
        programLanguage: applicationDetails.language,
        programDuration: applicationDetails.duration,
        UE_ID_Number: applicationDetails?.ueIDNumber,
        FirstName: applicationDetails?.referralFirstName,
        LastName: applicationDetails?.referralLastName,
      }),
    };

    return mappings;
  } catch (error) {
    throw error;
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig =
    filledBy === "agent" ? salesforceAgentConfig : salesforceStudentConfig;
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      if (applicationDetails[salesforceConfig[object][0]]) {
        for (const record of applicationDetails[salesforceConfig[object][0]]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[salesforceConfig[object][0]]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    }
  }

  return result;
}
