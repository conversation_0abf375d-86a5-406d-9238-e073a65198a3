import * as AWS from "aws-sdk";

const sts = new AWS.STS();

export const getS3PdfObject = async (bucket, path, roleArn): Promise<any> => {
  try {
    const s3 = await getS3CredentialsByRole(roleArn);
    console.log(bucket, path, roleArn);
    const params = {
      Bucket: bucket,
      Key: path,
    };
    const data = await s3.getObject(params).promise();
    return data.Body as Buffer;
  } catch (err) {
    console.log("[SERVER ERROR] Get PdfObject Failed", +err);
    throw err;
  }
};

export const getS3CredentialsByRole = async (roleArn): Promise<any> => {
  try {
    const sessionName = `Session-${Date.now()}`;
    const param: AWS.STS.AssumeRoleRequest = {
      RoleArn: roleArn,
      RoleSessionName: sessionName,
    };
    const data = await new Promise((resolve, reject) => {
      sts.assumeRole(param, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    const credentials = data["Credentials"];
    const s3Credentials = new AWS.S3({
      accessKeyId: credentials.AccessKeyId,
      secretAccessKey: credentials.SecretAccessKey,
      sessionToken: credentials.SessionToken,
    });
    return s3Credentials;
  } catch (err) {
    console.log("[Server Error] Get S3Credentials by role failed" + err);
    throw err;
  }
};
