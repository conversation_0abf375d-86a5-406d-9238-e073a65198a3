import { handlerPath } from "@libs/handler-resolver";

export const oapStudentNotification = {
  handler: `${handlerPath(__dirname)}/handler.handleStudentEmailNotification`,
  name: "oap-notifications-${self:provider.stage}",
  events: [
    {
      sns: {
        arn: "${self:provider.environment.OAP_NOTIFICATION_TOPIC_ARN}",
        topicName: "${self:provider.environment.OAP_NOTIFICATION_TOPIC_NAME}",
      },
    },
  ],
  timeout: 60,
};
