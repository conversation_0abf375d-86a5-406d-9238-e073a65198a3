import { middyfy } from '@libs/lambda';
import { SQSEvent } from "aws-lambda";

export const sqsHandler = async (event: SQSEvent) => {
  console.log(`Received ${event?.Records?.length} messages from SQS queue`);
  console.log("Record Data", event?.Records);

  for (const record of event.Records) {
    const messageBody = record.body;
    console.log(`Processing message with ID: ${record.messageId}`);
    console.log({ record });

    console.log(`messageBody: ${messageBody}`);

    // Log the source queue ARN
    const sourceQueueArn = record.eventSourceARN;
    console.log(`Message received from queue ARN: ${sourceQueueArn}`);

    console.log(`Processed message successfully`);
  }
};
export const handler = middyfy(sqsHandler);