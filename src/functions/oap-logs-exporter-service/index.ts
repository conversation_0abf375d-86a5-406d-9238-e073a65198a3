import { handlerPath } from '@libs/handler-resolver';

export const oapLogsExporter = {
  handler: `${handlerPath(__dirname)}/oapLogsExporterHandler.oapLogsExporterRequests`,
  name: 'oap-logs-exporter-${self:provider.stage}',
  events: [
    {
      cloudwatchLog: {
        logGroup: "oap-loggers-${self:provider.stage}",
      },
    },
    {
      cloudwatchLog: {
        logGroup: "lim-oap-csv-exporter-kpis-${self:provider.stage}",
      },
    },
  ]
};