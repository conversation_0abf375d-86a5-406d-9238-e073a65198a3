const AWS = require("aws-sdk");
const parquet = require("parquetjs");
const zlib = require('zlib');
const path = require("path");
const fs = require("fs");
const s3 = new AWS.S3();

const lim_oap_kpis_schema = new parquet.ParquetSchema({
    statusMessage: { type: 'UTF8' },
    executionStatus: { type: 'UTF8' },
    applicationRecordsExported: { type: 'INT64' },
    documentsExported: { type: 'INT64' },
    exportDurationSec: { type: 'DOUBLE' },
    timestamp: { type: 'UTF8' },
});

const oap_handlers_schema = new parquet.ParquetSchema({
    correlationId: { type: 'UTF8' },
    message: { type: 'UTF8' },
    type: { type: 'UTF8' },
    event: { type: 'UTF8' },
    statusCode: { type: 'INT64' },
    details: { type: 'UTF8' },
    brand: { type: 'UTF8' },
    version: { type: 'UTF8' },
    timestamp: { type: 'UTF8' },
});

export const logsToLambda = async (event) => {
    try {
        const bufferData = Buffer.from(event.awslogs.data, 'base64');
        const dateObj = new Date();
        const epochTimestamp = dateObj.getTime();
        const decompressedData = zlib.unzipSync(bufferData).toString('utf-8');
        console.log('decompressedData', decompressedData)
        const logEvents = JSON.parse(decompressedData);
        const logGroup = logEvents.logGroup;

        let schema, bucketName;

        if (logGroup === `lim-oap-csv-exporter-kpis-${process.env.STAGE}`) {
            schema = lim_oap_kpis_schema;
            bucketName = process.env.LIM_OAP_LOGS_EXPORTS_BUCKET_NAME;
        } else if (logGroup === `oap-loggers-${process.env.STAGE}`) {
            schema = oap_handlers_schema;
            bucketName = process.env.OAP_LOGS_EXPORTS_BUCKET_NAME;
        } else {
            throw new Error('Unsupported log group.');
        }

        const tempFilePath = path.join("/tmp", `${epochTimestamp}_data.parquet`);

        const writer = await parquet.ParquetWriter.openFile(schema, tempFilePath);
        await Promise.all(logEvents.logEvents.map(async (logEvent) => {
            const messageStartIndex = logEvent.message.indexOf('{');
            const jsonMessage = logEvent.message.slice(messageStartIndex);
            const message = JSON.parse(jsonMessage);

            console.log('Message:', message)
            await writer.appendRow(message);
        }));
        await writer.close();

        const params = {
            Bucket: bucketName,
            Key: `logs/${epochTimestamp}_data.parquet`,
            Body: fs.createReadStream(tempFilePath)
        };

        await s3.upload(params).promise();

        console.log('Parquet file uploaded to S3');
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
};

