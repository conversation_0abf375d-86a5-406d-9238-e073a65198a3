{"Records": [{"messageId": "14e352e8-d35c-4db8-9808-7ad496b53d19", "receiptHandle": "AQEBs06pxTRd7/W8nAlUeZc02Q5Ghy0wZcqNNdSGyP7KIYJHZppRepsY6oAkDHiNx76jf8wStxEwkltR6zltWGVXzWmkHmcti3/RGe/7n+W10o6NHWFg1CUi2BRB+Vi9x85X4BwmMXwzNk8F9JSz5sGL85VspNj5rPilwZnfQM4FtvB0GwPbo8xgDKBvMCZsZZyd1ljeTBc+MNdzDfscxOg2m77XNMWildc1dldbkctZlYPUuQNYMo+JU4qgZaWxv7YLs5ycT1DSJpczo9/X/9jAWPM2RxzxdUw4YAX7Sb7o/s8=", "body": "{\"email\":\"<EMAIL>\",\"applicationId\":\"c60774fe-7845-488b-82dc-cd98f1ebc8b3\",\"applicationStatus\":\"inProgress\",\"firstName\":\"Test unfc 01\",\"lastName\":\"A\",\"mobilePhone\":{\"number\":\"***********\",\"numberWithCode\":\"+1***********\",\"dialCode\":\"1\",\"countryCode\":\"us\"},\"country\":\"AF\",\"programDisplayName\":\"Honours Bachelor of Business Administration\",\"contactRecordTypeId\":\"01267000000ohkrAAA\",\"opportunityApplicationSource\":\"Agent Portal\",\"accountRecordTypeId\":\"0120O0000007L8XQAU\",\"leadSource\":\"Agent\",\"businessUnit\":\"a0g67000030qTGBAA2\",\"program\":\"a016700001KTzt5AAD\",\"opportunityRecordTypeId\":\"01267000000QgoBAAS\",\"SK\":\"UNFC_c60774fe-7845-488b-82dc-cd98f1ebc8b3\",\"stage\":\"Application\",\"sectionLabel\":\"EMERGENCY_CONTACT\",\"businessUnitFilter\":\"UNFC\",\"applicationFilledBy\":\"agent\",\"createdAt\":\"2024-09-20T05:36:57.742Z\",\"brand\":\"UNFC\",\"countryDisplayName\":\"Afghanistan\",\"sections\":[{\"name\":\"Additional Documents\",\"displayOrder\":6,\"status\":false},{\"name\":\"Applicant Information\",\"displayOrder\":2,\"status\":true},{\"name\":\"Education History\",\"displayOrder\":4,\"status\":false},{\"name\":\"Emergency Contact\",\"displayOrder\":3,\"status\":true},{\"name\":\"Program Information\",\"displayOrder\":1,\"status\":true},{\"name\":\"Review & Submit\",\"displayOrder\":9,\"status\":false},{\"name\":\"Standardized Test\",\"displayOrder\":5,\"status\":false},{\"name\":\"Transfer Credits\",\"displayOrder\":8,\"status\":false}],\"fullName\":\"Test unfc 01 A\",\"applicationSource\":\"UNFC\",\"levelDisplayName\":\"Undergraduate\",\"level\":\"a0S0O00000UAl6iUAD\",\"pricebookId\":\"01s67000004GstFAAS\",\"updatedAt\":\"2024-09-20T05:41:23.367Z\",\"progress\":68,\"leadRecordTypeId\":\"01267000000QgoAAAS\",\"appId\":\"UNF0000000040\",\"PK\":\"<EMAIL>\",\"locationDisplayName\":\"Niagara Falls\",\"location\":\"Niagara Falls\",\"programDeliveryDisplayName\":\"On-Campus\",\"programDelivery\":\"On-Campus\",\"intakeDisplayName\":\"October 2024\",\"intake\":\"2024-10-01\",\"prefixDisplayName\":\"Dr\",\"prefix\":\"Dr\",\"middleName\":\"kumar\",\"preferredName\":\"1test\",\"birthDate\":\"2014-09-03\",\"genderDisplayName\":\"Man\",\"gender\":\"Male\",\"citizenshipDisplayName\":\"Aland Islands\",\"citizenship\":\"AX\",\"citizenshipDocument\":[{\"documentId\":\"501868c0-d55e-46b3-8f32-52d679cdf303\"}],\"passportNumber\":\"1234\",\"passportIssueDate\":\"2024-09-04\",\"passportExpiryDate\":\"2024-09-30\",\"passportIssuingCountryDisplayName\":\"Afghanistan\",\"passportIssuingCountry\":\"AF\",\"passportDocument\":[{\"documentId\":\"d87e1880-58c8-4dc3-a8e7-b4985b223247\"}],\"canadaResidencyStatusDisplayName\":\"Citizen\",\"canadaResidencyStatus\":\"Citizenship\",\"primaryLanguageDisplayName\":\"English\",\"primaryLanguage\":\"English\",\"hasDisability\":\"No\",\"streetAddress\":\"test street\",\"city\":\"test city\",\"postalCode\":\"001002\",\"isPermanentAddressSame\":\"No\",\"phoneNumber\":{\"dialCode\":\"1\",\"countryCode\":\"us\",\"number\":\"94594994349\",\"numberWithCode\":\"+194594994349\"},\"alternatePhoneNumber\":{\"dialCode\":\"1\",\"countryCode\":\"us\",\"number\":\"00001202020\",\"numberWithCode\":\"+100001202020\"},\"emergencyContactFirstName\":\"test em\",\"emergencyContactSurname\":\"em\",\"emergencyContactRelationshipDisplayName\":\"Spouse\",\"emergencyContactRelationship\":\"Spouse\",\"emergencyContactPhoneNumber\":{\"dialCode\":\"1\",\"countryCode\":\"us\",\"number\":\"04304304300\",\"numberWithCode\":\"+104304304300\"},\"emergencyContactEmail\":\"<EMAIL>\",\"documents\":null,\"requestId\":\"9b01bcac-1fb1-45fd-ac06-553f1b49a9d4\",\"APIKEY\":\"yphESfRh2o5J7L87WsKfh2MIBWSdPHev6TNPSGNZ\"}", "attributes": {"ApproximateReceiveCount": "1", "AWSTraceHeader": "Root=1-66ed0b03-148bc39f75d46e8747f29890;Parent=a2260f0926eb4d8e;Sampled=0;Lineage=1:c3b4e8bb:0", "SentTimestamp": "1726810884205", "SequenceNumber": "18888807660066031616", "MessageGroupId": "c60774fe-7845-488b-82dc-cd98f1ebc8b3", "SenderId": "AIDAWYJAWPFU7SUQGUJC6", "MessageDeduplicationId": "7c83c8203c47c9243634cb7f2dba74062cd32b2449c5d3191db4406a90a9df4b", "ApproximateFirstReceiveTimestamp": "1726810884205"}, "messageAttributes": {"source": {"stringValue": "UNFC", "stringListValues": [], "binaryListValues": [], "dataType": "String"}}, "md5OfMessageAttributes": "1288bdc64fbdce4a288d94da1b094274", "md5OfBody": "3571dfabcee1c6738e2ea4cd4935836f", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:eu-west-1:933713876074:DEV-UNFC-EIP-QUEUE.fifo", "awsRegion": "eu-west-1"}]}