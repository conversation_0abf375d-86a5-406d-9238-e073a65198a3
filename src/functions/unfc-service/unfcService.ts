import { CloudWatchLoggerService, LoggerEnum } from "@gus-eip/loggers";
import { postDataSf } from "src/connectors/salesforce-connectors";
import {
  salesforceAgentConfig,
  salesforceAgentSubObjectsConfig,
  salesforceStudentConfig,
  salesforceStudentSubObjectsConfig,
} from "./salesforceconfig";

const cloudWatchLoggerService = new CloudWatchLoggerService(
  process.env.REGION,
  process.env.LOGGER_LOG_GROUP_NAME,
  process.env.TEAMS_WEBHOOK_URL,
  true
);
const loggerEnum = new LoggerEnum();

export const handleUnfcSfSaveOrUpdateRequests = async (event) => {
  console.log(JSON.stringify(event));

  const brand = "UNFC";
  for (let record of event) {
    let applicationDetails = JSON.parse(record["body"]);
    console.log("App details -->", applicationDetails);
    const requestId = applicationDetails.requestId;
    const filledBy = applicationDetails?.applicationFilledBy;
    const APIKEY = applicationDetails.APIKEY;
    const currentUTC = new Date().toISOString();
    let request = {};
    let referenceMappedDetails;
    console.log(
      "Modified Application details document -->",
      applicationDetails.documents
    );
    if (applicationDetails?.privacyPolicyConsent) {
      applicationDetails.privacyPolicyConsent =
        !applicationDetails?.privacyPolicyConsent;
    }

    if (applicationDetails?.fullName) {
      applicationDetails.fullName =
        applicationDetails.fullName.length >= 80
          ? applicationDetails.fullName.substring(0, 79)
          : applicationDetails.fullName;
    }

    try {
      if (
        !applicationDetails?.isPermanentAddressSame ||
        applicationDetails?.isPermanentAddressSame === "No"
      ) {
        applicationDetails = {
          ...applicationDetails,
          permanentStreetAddress: applicationDetails?.streetAddress,
          permanentCity: applicationDetails?.city,
          permanentPostalCode: applicationDetails?.postalCode,
          permanentCountry: applicationDetails?.country,
          permanentCountryDisplayName: applicationDetails?.countryDisplayName,
        };
      }
      if (applicationDetails?.applicationStatus === "submitted") {
        applicationDetails.visaApplication = [
          {
            visaRequired:
              applicationDetails?.visaRequirement === "Yes" ? true : false,
            visaNumber: applicationDetails?.visaNumber,
          },
        ];
        const identityDocumentObject = [
          {
            identityNumber: applicationDetails.passportNumber,
            identityIssueDate: applicationDetails.passportIssueDate,
            identityExpiryDate: applicationDetails.passportExpiryDate,
            identityIssuingCountry:
              applicationDetails.passportIssuingCountryDisplayName,
            identityType: "Passport",
          },
        ];
        applicationDetails.identityDocumentObject = identityDocumentObject;
        const mappings = {
          institutions: "educationHistory",
        };
        for (const [sourceKey, targetKey] of Object.entries(mappings)) {
          if (applicationDetails.hasOwnProperty(sourceKey)) {
            applicationDetails[targetKey] = applicationDetails[sourceKey];
          }
        }
        if (applicationDetails?.institutions[0]?.institutionNameDisplayName) {
          console.log(
            "InstitutionName",
            applicationDetails?.institutions[0]?.institutionNameDisplayName
          );
          applicationDetails.leadInstitutionName =
            applicationDetails?.institutions[0]?.institutionNameDisplayName;
        }

        if (applicationDetails?.educationHistory) {
          let initialCount = 1;
          applicationDetails?.educationHistory.forEach((institutions) => {
            institutions["institutionOrder"] = initialCount;
            initialCount++;
            if (institutions?.institutionNameDisplayName === "Other") {
              institutions["institutionNameDisplayName"] =
                institutions?.otherInstitutionName;
              delete institutions["institutionName"];
            }
          });
        }
        referenceMappedDetails = await opportunityFileReferenceMapping(
          applicationDetails
        );
        applicationDetails = referenceMappedDetails.appDetails;

        console.log("app details ->", applicationDetails);

         applicationDetails?.documents.forEach((doc) => {
            if (doc.documentType === 'ID/Passport') {
              doc.isIdentityInformationAccurate =
                applicationDetails.isIdentityInformationAccurate === "Yes";
            }
          });

        applicationDetails = {
          ...applicationDetails,
          ...buildMappings(applicationDetails),
        };
      }
      request["sectionLabel"] = applicationDetails.sectionLabel;
      request["email"] = applicationDetails.email;
      request["applicationId"] = applicationDetails.applicationId;
      request["requestId"] = requestId;
      request["messageId"] = record.messageId;
      if (referenceMappedDetails?.institutionData) {
        const mappedInstitutionData = {
          ...request,
          ...(await mapSalesforceObject(
            referenceMappedDetails?.institutionData,
            filledBy
          )),
        };
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };

        await Promise.all([
          postDataSf(mappedInstitutionData, APIKEY, filledBy),
          postDataSf(request, APIKEY, filledBy),
        ]);
      } else {
        request = {
          ...request,
          ...(await mapSalesforceObject(applicationDetails, filledBy)),
        };

        console.log("request -->", request);
        await postDataSf(request, APIKEY, filledBy);
      }
      await cloudWatchLoggerService.log(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.OPERATION_COMPLETED,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        "Successfully saved to salesforce",
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    } catch (error) {
      console.log(error);
      await cloudWatchLoggerService.error(
        requestId,
        currentUTC,
        loggerEnum.Component.OAP_HANDLERS,
        loggerEnum.Component.OAP_BACKEND,
        loggerEnum.Component.GUS_EIP_SERVICE,
        loggerEnum.Event.SAVED_TO_SALESFORCE,
        loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        error.errorDetails
          ? typeof error.errorDetails === "string"
            ? error.errorDetails
            : JSON.stringify(error.errorDetails)
          : JSON.stringify(error),
        brand,
        applicationDetails.email,
        `oap-handlers/${applicationDetails.applicationId}/${requestId}`,
        "Application_Form_Id__c",
        applicationDetails.applicationId,
        "Opportunity_Application_Account",
        applicationDetails.applicationId
      );
    }
  }
};

function opportunityFileReferenceMapping(appDetails) {
  if (appDetails && Array.isArray(appDetails.documents)) {
    const documentMap = new Map();
    appDetails.documents.forEach((doc) => {
      documentMap.set(doc.documentId, doc);
    });

    const addReferences = (documentIds, referenceKey, referenceValue) => {
      documentIds.forEach((docId) => {
        const matchingDocument = documentMap.get(docId);
        if (matchingDocument) {
          matchingDocument[referenceKey] = referenceValue;
        }
      });
    };
    let institutionData = {
      documents: [],
      educationHistory: [],
    };
    if (Array.isArray(appDetails.educationHistory)) {
      appDetails.educationHistory.forEach((institution) => {
        const institutionOrder = institution.institutionOrder;
        const testCertificates = institution.testCertificateId || [];

        const documentIds = testCertificates.map((doc) => doc.documentId);

        addReferences(
          documentIds,
          "eduReference",
          `@{EducationHistoryRecord__c_${institutionOrder}.id}`
        );

        institutionData.documents.push(
          ...documentIds.map((docId) => documentMap.get(docId)).filter(Boolean)
        );
        institutionData.educationHistory.push(institution);
      });

      appDetails.educationHistory = [];
      appDetails.documents = appDetails.documents.filter(
        (doc) =>
          !institutionData.documents.some(
            (institutionDoc) => institutionDoc.documentId === doc.documentId
          )
      );
    } else {
      console.error("No institutions found in appDetails. Nothing to process.");
    }

    let attachReportReferenceValue = "@{LanguageProficiencyRecord__c_2.id}";
    if (!Array.isArray(appDetails.report)) {
      attachReportReferenceValue = "@{LanguageProficiencyRecord__c_1.id}";
    }

    const categories = [
      {
        key: "passportDocument",
        referenceKey: "identityReference",
        referenceValue: "@{IdentityInfoRecord__c_1.id}",
      },
      {
        key: "report",
        referenceKey: "testReference",
        referenceValue: "@{LanguageProficiencyRecord__c_1.id}",
      },
      {
        key: "attatchReport",
        referenceKey: "testReference",
        referenceValue: attachReportReferenceValue,
      },
    ];

    categories.forEach(({ key, referenceKey, referenceValue }) => {
      if (Array.isArray(appDetails[key])) {
        const documentIds = appDetails[key].map((doc) => doc.documentId);
        addReferences(documentIds, referenceKey, referenceValue);
      }
    });

    console.log("Processed appDetails.documents:", appDetails.documents);
    console.log("Institution data moved:", institutionData);

    return { appDetails, institutionData };
  } else {
    console.error("Invalid or missing appDetails.documents.");
  }

  return {
    appDetails,
    institutionData: { documents: [], educationHistory: [] },
  };
}

function buildMappings(applicationDetails) {
  try {
    const {
      emergencyContactFirstName,
      emergencyContactSurname,
      emergencyContactRelationship,
      emergencyContactPhoneNumber,
      emergencyContactEmail,
      testName,
      testDate,
      overallScore,
      listeningScore,
      speakingScore,
      readingScore,
      writingScore,
      testReportFormNo,
      proficiencyQualification,
    } = applicationDetails;

    let mappings = {
      connections: [
        {
          emergencyContactFirstName,
          emergencyContactSurname,
          emergencyContactRelationship,
          emergencyContactPhoneNumber,
          emergencyContactEmail,
          connectionType: "Emergency",
        },
      ],
      languageProficiency: [
        {
          proficiencyQualification: proficiencyQualification,
          testName: testName,
          testDate,
          overallScore,
          listeningScore,
          speakingScore,
          readingScore,
          writingScore,
          testReportFormNo,
        },
      ],
      miscDetails: JSON.stringify({
        Coop_WorkPermitRequired__c: applicationDetails?.coopWorkPermitRequired,
        Coop_WorkPermitNumber__c: applicationDetails?.coopWorkPermitNumber,
        Coop_WorkPermitIssueDate__c:
          applicationDetails?.coopWorkPermitIssueDate,
        Coop_WorkPermitExpiryDate__c:
          applicationDetails?.coopWorkPermitExpiryDate,
        Has_Disability__c: applicationDetails?.hasDisability,
        Disability__c: applicationDetails?.pleaseSpecifyDisability,
        EducationHistory_Expelled__c: applicationDetails?.isSuspended,
        EducationHistoryComments__c: applicationDetails?.suspensionReason,
        TransferCredits__c: applicationDetails?.programOfStudy,
        Comments__c: applicationDetails?.pleaseSpecify,
        Province__c: applicationDetails?.province,
        Permanent_Province__c: applicationDetails?.permanentProvince,
      }),
    };

    return mappings;
  } catch (error) {
    throw error;
  }
}

async function mapSalesforceObject(applicationDetails, filledBy): Promise<any> {
  let result = {};
  const salesforceConfig =
    filledBy === "agent" ? salesforceAgentConfig : salesforceStudentConfig;
  const salesforceSubObjectsConfig =
    filledBy === "agent"
      ? salesforceAgentSubObjectsConfig
      : salesforceStudentSubObjectsConfig;
  for (const object in salesforceConfig) {
    if (Array.isArray(salesforceConfig[object])) {
      if (applicationDetails[salesforceConfig[object][0]]) {
        for (const record of applicationDetails[salesforceConfig[object][0]]) {
          let mappedObject = await mapValues(
            record,
            salesforceSubObjectsConfig[salesforceConfig[object][0]]
          );
          if (mappedObject && Object.keys(mappedObject).length > 0) {
            if (!result[object]) {
              result[object] = [];
            }
            result[object].push(mappedObject);
          }
        }
      }
    } else {
      let mappedObject = await mapValues(
        applicationDetails,
        salesforceConfig[object]
      );
      console.log(mappedObject);
      if (mappedObject && Object.keys(mappedObject).length > 0) {
        result[object] = mappedObject;
      }
    }
  }
  return result;
}
async function mapValues(source, mapping) {
  const result = {};

  for (const [sourceKey, targetKey] of Object.entries(mapping)) {
    let value;

    if (sourceKey.includes(",")) {
      // Handle multiple keys separated by commas
      const keys = sourceKey.split(",");
      const values = [];

      for (const key of keys) {
        if (key.includes(".")) {
          // Handle nested properties
          const nestedKeys = key.trim().split(".");
          let nestedValue = source;

          for (const nestedKey of nestedKeys) {
            nestedValue = nestedValue ? nestedValue[nestedKey] : undefined;
          }
          if (nestedValue !== undefined) {
            values.push(nestedValue);
          }
        } else {
          // Handle simple keys
          const simpleValue = source[key.trim()];
          if (simpleValue !== undefined) {
            values.push(simpleValue);
          }
        }
      }

      // Join all collected values into a single string
      value = values.join(" ");
    } else if (sourceKey.includes(".")) {
      // Handle single nested property
      const keys = sourceKey.split(".");
      value = source;

      for (const key of keys) {
        value = value ? value[key] : undefined;
      }
    } else {
      // Handle single simple property
      value = source[sourceKey];
    }

    if (value !== undefined) {
      if (Array.isArray(targetKey)) {
        // If the targetKey is an array, map each value to the corresponding sourceKey
        targetKey.forEach((key) => {
          result[key] = value;
        });
      } else {
        // Otherwise, map the value directly
        result[targetKey] = value;
      }
    }
  }

  return result;
}
