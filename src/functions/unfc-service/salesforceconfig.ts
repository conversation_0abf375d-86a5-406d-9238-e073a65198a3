export const salesforceAgentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    leadInstitutionName: "Additional_Info__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    levelOfStudy: "Education_Level__c",
    institutionNameDisplayName: ["Institute_Name__c", "InstitutionName__c"],
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    studyCompleted: "Study_completed__c",
    otherLocationName: "Location__c",
    institutionName: "Institution_Id__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testName: "TestProvider__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
    testReportFormNo: "Test_link_Certification__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactSurname: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    emergencyContactEmail: "Email_c__c",
    connectionType: "Type__c",
  },
  teamMembers: {
    opportunityAccessLevel: "OpportunityAccessLevel",
    teamMemberRole: "TeamMemberRole",
    userId: "UserId",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
  visaApplication: {
    visaRequired: "Visa_Required__c",
    visaNumber: "visa_Number__c",
  },
};

export const salesforceAgentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  Connection__c: ["connections"],
  Visa_Application__c: ["visaApplication"],
  Lead: {
    leadSource: "LeadSource",
    brand: "Brand__c",
    countryDisplayName: ["Country", "Country__c"],
    program: "Programme__c",
    firstName: "FirstName",
    leadRecordTypeId: "RecordTypeId",
    lastName: "LastName",
    email: "Email",
    leadInstitutionName: "Name_of_the_institution__c",
    "phoneNumber.numberWithCode": "Phone",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    agentAccountId: "AgentAccount__c",
    agentContactId: "Agent_Contact__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    "phoneNumber.numberWithCode": "PersonMobilePhone",
    countryDisplayName: ["Country__c", "PersonMailingCountry"],
    middleName: "Middle_Name__c",
    preferredName: "PreferedFirstName__c",
    birthDate: "DateOfBirth__c",
    gender: "Gender__c",
    citizenshipDisplayName: "Citizenship__c",
    canadaResidencyStatus: "Citizenship_Status__c",
    primaryLanguage: "Primary_Language__c",
    streetAddress: "PersonMailingStreet",
    city: ["PersonMailingCity", "gaconnector_City__c"],
    postalCode: "PersonMailingPostalCode",
    "alternatePhoneNumber.numberWithCode": "Mobile__c",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    agentContactUserId: "OwnerId",
    placeOfBirth: "PlaceOfBirth__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
    permanentStreetAddress: "ShippingStreet",
    permanentCity: "ShippingCity",
    permanentCountryDisplayName: "ShippingCountry",
    permanentPostalCode: "ShippingPostalCode",
    brand: "Brand__c",
  },
  Opportunity: {
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountMobile__c",
    programDisplayName: "Name",
    program: "Programme__c",
    location: "Location__c",
    programDelivery: "Delivery_Mode__c",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    citizenshipDisplayName: "Citizenship__c",
    permanentCity: "gaconnector_City__c",
    agreeToUniversityDisclosure: "DeclarationInfoProvided__c",
    appId: "ApplicationId__c",
    agentContactId: "Agent_Contact__c",
    agentAccountId: "AgentAccount__c",
    accountManagerUserId: "BusinessDeveloper__c",
    agentContactUserId: "OwnerId",
    opportunityRecordTypeId: "RecordTypeId",
    opportunityApplicationSource: "ApplicationSource__c",
    businessUnit: "BusinessUnit__c",
    stage: "StageName",
    applicationId: "ApplicationFormId__c",
    pricebookId: "Pricebook2Id",
    email: "Email__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    acceptedPrivacyPolicy: "DeclarationPrivacyPolicy__c",
    submittedDate: "DeclarationDate__c",
    submissionSignature: "DeclarationSignature__c",
    admissionStage: "AdmissionsStage__c",
    typeOfVisa: "VisaType__c",
    visaIssueDate: "VisaIssueDate__c",
    visaExpiryDate: "VisaExpiryDate__c",
    miscDetails: "Application_Misc_Details__c",
  },
  Application__c: {
    firstName: "First_Name__c",
    applicationId: "Application_Form_Id__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    agentAccountId: "Agent_Account__c",
    agentContactId: "Agent_Contact__c",
    permanentStreetAddress: "Street_Address__c",
    permanentCity: "City__c",
    permanentPostalCode: "Postcode__c",
    emergencyContactFirstName: "First_Name_Emergency__c",
    emergencyContactSurname: "Surname_Emergency__c",
    "emergencyContactPhoneNumber.numberWithCode": "Emergency_Phone_Number__c",
    emergencyContactEmail: "Emergency_Email__c",
    leadInstitutionName: "Institution1__c",
    fullName: "Name",
    lastName: "Last_Name__c",
    email: "Email__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    levelDisplayName: "Level__c",
    programDisplayName: "Program_Of_Study__c",
    intake: "Intake__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
  OpportunityTeamMember: ["teamMembers"],
};

export const salesforceStudentSubObjectsConfig = {
  documents: {
    applicationId: "ApplicationId__c",
    documentType: "DocumentType__c",
    documentName: ["Name", "OriginalValue__c", "FilePath__c"],
    opportunityId: "Opportunity__c",
    path: ["S3FileName__c", "FullUrl__c"],
    bucketName: "BucketName__c",
    leadInstitutionName: "Additional_Info__c",
    eduReference: "Related_Education_History__c",
    testReference: "Related_Language_Proficiency__c",
    identityReference: "Related_Proof_of_Identity__c",
    isIdentityInformationAccurate: "Is_Info_Accurate__c",
  },
  educationHistory: {
    levelOfStudy: "Education_Level__c",
    institutionNameDisplayName: ["Institute_Name__c", "InstitutionName__c"],
    firstEnrolmentDate: "EnrolmentDateYear__c",
    lastEnrolmentDate: "GraduationDate__c",
    studyCompleted: "Study_completed__c",
    otherLocationName: "Location__c",
    institutionName: "Institution_Id__c",
  },
  languageProficiency: {
    proficiencyQualification: "ProficiencyQualification__c",
    testName: "TestProvider__c",
    testDate: "TestDate__c",
    overallScore: "TestScore__c",
    listeningScore: "Listening_Score__c",
    speakingScore: "Speaking_Score__c",
    readingScore: "Reading_Score__c",
    writingScore: "Writing_Score__c",
    testReportFormNo: "Test_link_Certification__c",
  },
  connections: {
    emergencyContactFirstName: "First_Name_c__c",
    emergencyContactSurname: "Last_Name__c",
    emergencyContactRelationship: "Relationship__c",
    "emergencyContactPhoneNumber.numberWithCode": "Phone_c__c",
    emergencyContactEmail: "Email_c__c",
    connectionType: "Type__c",
  },
  identityDocumentObject: {
    identityNumber: "Identity_Document_Number__c",
    identityIssueDate: "Issue_Date__c",
    identityExpiryDate: "Expiry_Date__c",
    identityType: "Identity_Type__c",
    identityIssuingCountry: "Issuing_Country__c",
  },
  visaApplication: {
    visaRequired: "Visa_Required__c",
    visaNumber: "visa_Number__c",
  },
};

export const salesforceStudentConfig = {
  IdentityInfoRecord__c: ["identityDocumentObject"],
  LanguageProficiencyRecord__c: ["languageProficiency"],
  EducationHistoryRecord__c: ["educationHistory"],
  OpportunityFile__c: ["documents"],
  Connection__c: ["connections"],
  Visa_Application__c: ["visaApplication"],
  Lead: {
    leadSource: "LeadSource",
    program: "Programme__c",
    firstName: "FirstName",
    lastName: "LastName",
    email: "Email",
    countryDisplayName: ["Country", "Country__c"],
    opportunityApplicationSource: "ApplicationSource__c",
    leadRecordTypeId: "RecordTypeId",
    "phoneNumber.numberWithCode": "Phone",
    brand: "Brand__c",
    businessUnit: "BusinessUnit__c",
    businessUnitFilter: "BusinessUnitFilter__c",
    leadInstitutionName: "Name_of_the_institution__c",
    "utmParams.utmSource": "pi__utm_source__c",
    "utmParams.utmMedium": "pi__utm_medium__c",
    "utmParams.utmCampaign": "pi__utm_campaign__c",
    "utmParams.utmContent": "pi__utm_content__c",
    "utmParams.utmTerm": "pi__utm_term__c",
    "utmParams.utmNetwork": "utm_network__c",
    "utmParams.utmReferrer": "Utm_Referrer__c",
    "utmParams.sourceWebsite": "Source_Website__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmailSync__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Account: {
    firstName: "FirstName",
    lastName: "LastName",
    email: "PersonEmail",
    "phoneNumber.numberWithCode": "PersonMobilePhone",
    "alternatePhoneNumber.numberWithCode": "Mobile__c",
    countryDisplayName: "Country__c",
    middleName: "Middle_Name__c",
    preferredName: "PreferedFirstName__c",
    birthDate: "DateofBirth__c",
    gender: "PersonGenderIdentity",
    citizenshipDisplayName: "Citizenship__c",
    canadaResidencyStatus: "Citizenship_Status__c",
    primaryLanguage: "Primary_Language__c",
    streetAddress: "PersonMailingStreet",
    city: "gaconnector_City__c",
    postalCode: "PersonMailingPostalCode",
    permanentStreetAddress: "ShippingStreet",
    permanentCity: "ShippingCity",
    permanentCountryDisplayName: "ShippingCountry",
    permanentPostalCode: "ShippingPostalCode",
    businessUnit: "BusinessUnit__c",
    accountRecordTypeId: "RecordTypeId",
    brand: "Brand__c",
    placeOfBirth: "PlaceOfBirth__c",
    privacyPolicyConsent: [
      "HasOptedOutOfMarketingEmail__c",
      "HasOptedOutOfGUSMarketingEmail__c",
    ],
  },
  Opportunity: {
    partnerInstitution: "PathwayProviderId__c",
    "phoneNumber.numberWithCode": "AccountMobile__c",
    programDisplayName: "Name",
    program: "Programme__c",
    location: "Location__c",
    programDelivery: "Delivery_Mode__c",
    intake: ["CloseDate", "Product_Intake_Date__c"],
    citizenshipDisplayName: "Citizenship__c",
    permanentCity: "gaconnector_City__c",
    agreeToUniversityDisclosure: "DeclarationInfoProvided__c",
    appId: "ApplicationId__c",
    opportunityRecordTypeId: "RecordTypeId",
    businessUnit: "BusinessUnit__c",
    stage: "StageName",
    applicationId: "ApplicationFormId__c",
    pricebookId: "Pricebook2Id",
    email: "Email__c",
    opportunityApplicationSource: "ApplicationSource__c",
    progress: "ApplicationProgress__c",
    isSubmitted: "ApplicationSubmitted__c",
    acceptedPrivacyPolicy: "DeclarationPrivacyPolicy__c",
    submittedDate: "DeclarationDate__c",
    submissionSignature: "DeclarationSignature__c",
    typeOfVisa: "VisaType__c",
    visaIssueDate: "VisaIssueDate__c",
    visaExpiryDate: "VisaExpiryDate__c",
    miscDetails: "Application_Misc_Details__c",
  },
  Application__c: {
    applicationRecordTypeId: "RecordTypeId",
    firstName: "First_Name__c",
    middleName: "Middle_Other_Name_s__c",
    email: "Email__c",
    citizenshipDisplayName: "Citizenship__c",
    birthDate: "Date_of_birth__c",
    gender: "Gender__c",
    permanentStreetAddress: "Street_Address__c",
    permanentCity: "City__c",
    postalCode: "Postcode__c",
    "phoneNumber.numberWithCode": "Mobile__c",
    emergencyContactFirstName: "First_Name_Emergency__c",
    emergencyContactSurname: "Surname_Emergency__c",
    "emergencyContactPhoneNumber.numberWithCode": "Emergency_Phone_Number__c",
    emergencyContactEmail: "Emergency_Email__c",
    applicationId: "Application_Form_Id__c",
    accountManagerUserId: "Business_Developer__c",
    agentContactUserId: "OwnerId",
    countryDisplayName: "Nationality__c",
    intake: ["Start_Date__c", "Intake__c"],
    fullName: "Name",
    programDisplayName: "Program_Of_Study__c",
    institutionName: "Institution1__c",
    passportExpiryDate: "Passport_Date_of_expiry__c",
    levelDisplayName: "Level__c",
  },
  OpportunityLineItem: {
    productId: "Product2Id",
  },
};
