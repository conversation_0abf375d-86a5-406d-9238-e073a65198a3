import { CognitoJwtVerifier } from "aws-jwt-verify";
import * as AWS from "aws-sdk";

export interface AuthEvent {
  headers: {
    [key: string]: string;
  };
  methodArn: string;
}

interface VerifyTokenParams {
  userPoolId: string;
  clientId: string;
  token: string;
}

export const handleCustomAuth = async (event: AuthEvent) => {
  try {
    const consumer = await getConsumerByKey(event.headers["x-api-key"]);

    if (!consumer) {
      return "unauthorized";
    }

    const cognitoEnvVar = `${consumer}_COGNITO`;

    console.log("congnitoENvc", consumer, cognitoEnvVar);

    if (!process.env[cognitoEnvVar]) {
      return "unauthorized";
    }

    let cognitoValue: VerifyTokenParams;
    try {
      cognitoValue = JSON.parse(
        process.env[cognitoEnvVar] || "{}"
      ) as VerifyTokenParams;
    } catch (error) {
      console.error("Error parsing Cognito environment variable:", error);
      return "unauthorized";
    }

    const token = event.headers["Authorization"];

    const verifyObject = {
      ...cognitoValue,
      token,
    };

    // Try to verify with the primary user pool
    let isValid = await verifyToken(verifyObject);

    if (isValid) {
      console.log(
        `Successfully authenticated with primary user pool for consumer: ${consumer}`
      );
      return "allow";
    }

    // If not valid and mutual authentication is enabled, try other user pools
    if (process.env.MUTUAL_AUTH_ENABLED === "true") {
      console.log(
        "Primary authentication failed, trying mutual authentication"
      );

      // First, prioritize checking the apphero Cognito pool
      if (process.env.DEV_APPHERO_COGNITO) {
        try {
          console.log("Trying to authenticate with apphero Cognito pool");
          const appheroConfig = JSON.parse(
            process.env.DEV_APPHERO_COGNITO || "{}"
          ) as VerifyTokenParams;

          console.log("appheroConfig", appheroConfig);

          isValid = await verifyToken({
            userPoolId: appheroConfig.userPoolId,
            clientId: appheroConfig.clientId,
            token,
          });

          if (isValid) {
            console.log("Successfully authenticated with apphero user pool");
            return "allow";
          }
        } catch (error) {
          console.error("Error verifying with apphero user pool:", error);
        }
      }
    }

    return isValid ? "allow" : "deny";
  } catch (error) {
    console.error("Error in handleCustomAuth:", error);
    return "unauthorized";
  }
};

export async function verifyToken(event: VerifyTokenParams): Promise<boolean> {
  const verifier = CognitoJwtVerifier.create({
    userPoolId: event.userPoolId,
    tokenUse: "access",
    clientId: event.clientId,
  });

  try {
    const payload = await verifier.verify(event.token);
    console.log("Token is valid. Payload:", payload);
    return true;
  } catch (error) {
    console.error("Token verification failed:", error);
    return false;
  }
}

export async function getConsumerByKey(apikey: string): Promise<string | null> {
  const dynamoDB = new AWS.DynamoDB.DocumentClient({
    region: process.env.REGION,
  });

  const params = {
    TableName: `gus-eip-consumer-${process.env.STAGE}`,
    Key: { PK: apikey },
  };

  try {
    const consumerDetails = await dynamoDB.get(params).promise();
    return consumerDetails?.Item?.consumer || null;
  } catch (error) {
    console.error("Error fetching consumer by key:", error);
    return null;
  }
}
