import {
  handleCustomAuth,
  AuthEvent,
} from "@functions/custom-auth-service/customAuthService";

type AuthCallback = (error: string | null, policy?: any) => void;

export const customAuthRequests = async (
  event: AuthEvent,
  _context: any,
  callback: AuthCallback
) => {
  try {
    console.log(JSON.stringify(event));
    const token = await handleCustomAuth(event);
    switch (token) {
      case "allow":
        callback(null, generatePolicy("user", "Allow", event.methodArn));
        break;
      case "deny":
        callback(null, generatePolicy("user", "Deny", event.methodArn));
        break;
      case "unauthorized":
        callback("Unauthorized");
        break;
      default:
        callback("Error: Invalid token");
    }
  } catch (error) {
    console.error("Error in customAuthRequests:", error);
    callback("Error: Internal Server Error");
  }
};

const generatePolicy = (
  principalId: string,
  effect: string,
  resource: string
) => {
  const authResponse: any = {
    principalId,
  };

  if (effect && resource) {
    const policyDocument = {
      Version: "2012-10-17",
      Statement: [
        {
          Action: "execute-api:Invoke",
          Effect: effect,
          Resource: resource,
        },
      ],
    };
    authResponse.policyDocument = policyDocument;
  }

  return authResponse;
};

export const handleAuthentication = customAuthRequests;
