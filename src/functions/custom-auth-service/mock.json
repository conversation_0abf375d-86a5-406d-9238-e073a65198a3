{"type": "REQUEST", "methodArn": "arn:aws:execute-api:eu-west-1:************:kjv7fhpkm7/dev/GET/student/oap", "resource": "/student/oap", "path": "/student/oap", "httpMethod": "ANY", "headers": {"Accept": "*/*", "Accept-Encoding": "gzip, deflate, br", "Authorization": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "Cache-Control": "no-cache", "CloudFront-Forwarded-Proto": "https", "CloudFront-Is-Desktop-Viewer": "true", "CloudFront-Is-Mobile-Viewer": "false", "CloudFront-Is-SmartTV-Viewer": "false", "CloudFront-Is-Tablet-Viewer": "false", "CloudFront-Viewer-ASN": "55836", "CloudFront-Viewer-Country": "IN", "Host": "oap-dev-api.apphero.io", "Postman-Token": "025bbc3d-08c3-42fc-9c6f-c2befbe7fc22", "User-Agent": "PostmanRuntime/7.41.2", "Via": "1.1 20eddc312f5fafe3d85effa2fe22f9e6.cloudfront.net (CloudFront)", "X-Amz-Cf-Id": "yxy380M9kYI2z4MBG0-WOSp1bPP940Mp3kWUuOO65_IOG7n7Uk-szA==", "X-Amzn-Trace-Id": "Root=1-66cea0fa-06e9a3ef6232dabf2fad409e", "X-Api-Key": "yphESfRh2o5J7L87WsKfh2MIBWSdPHev5TNPSGNZ", "X-Forwarded-For": "*************, *************", "X-Forwarded-Port": "443", "X-Forwarded-Proto": "https", "x-api-key": "rLknjx19jUaPaoqGtbLawha8ylaHwBs8zP6aoGrh"}, "multiValueHeaders": {"Accept": ["*/*"], "Accept-Encoding": ["gzip, deflate, br"], "Authorization": ["****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"], "Cache-Control": ["no-cache"], "CloudFront-Forwarded-Proto": ["https"], "CloudFront-Is-Desktop-Viewer": ["true"], "CloudFront-Is-Mobile-Viewer": ["false"], "CloudFront-Is-SmartTV-Viewer": ["false"], "CloudFront-Is-Tablet-Viewer": ["false"], "CloudFront-Viewer-ASN": ["55836"], "CloudFront-Viewer-Country": ["IN"], "Host": ["oap-dev-api.apphero.io"], "Postman-Token": ["025bbc3d-08c3-42fc-9c6f-c2befbe7fc22"], "User-Agent": ["PostmanRuntime/7.41.2"], "Via": ["1.1 20eddc312f5fafe3d85effa2fe22f9e6.cloudfront.net (CloudFront)"], "X-Amz-Cf-Id": ["yxy380M9kYI2z4MBG0-WOSp1bPP940Mp3kWUuOO65_IOG7n7Uk-szA=="], "X-Amzn-Trace-Id": ["Root=1-66cea0fa-06e9a3ef6232dabf2fad409e"], "x-api-key": ["yphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ"], "X-Forwarded-For": ["*************, *************"], "X-Forwarded-Port": ["443"], "X-Forwarded-Proto": ["https"]}, "queryStringParameters": {"mode": "AGENT", "name": "IBAT_EL"}, "multiValueQueryStringParameters": {"mode": ["AGENT"], "name": ["IBAT_EL"]}, "pathParameters": {}, "stageVariables": {}, "requestContext": {"resourceId": "6ivvem", "resourcePath": "/student/oap", "httpMethod": "GET", "extendedRequestId": "dM4XHFReDoEEW5g=", "requestTime": "28/Aug/2024:04:00:58 +0000", "path": "/student/oap", "accountId": "************", "protocol": "HTTP/1.1", "stage": "dev", "domainPrefix": "oap-dev-api", "requestTimeEpoch": *************, "requestId": "812e64ec-bcba-4655-8644-9d554b9a1eff", "identity": {"cognitoIdentityPoolId": null, "cognitoIdentityId": null, "apiKey": "yphESfRh2o5J7L87WsKfh2MIBWSdPDev4TNPSGNZ", "principalOrgId": null, "cognitoAuthenticationType": null, "userArn": null, "apiKeyId": "orq99ifmrl", "userAgent": "PostmanRuntime/7.41.2", "accountId": null, "caller": null, "sourceIp": "*************", "accessKey": null, "cognitoAuthenticationProvider": null, "user": null}, "domainName": "oap-dev-api.apphero.io", "deploymentId": "pko9n7", "apiId": "kjv7fhpkm7"}}