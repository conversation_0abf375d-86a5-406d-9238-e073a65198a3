import { SNS } from 'aws-sdk';

export class SnsService {
    private readonly sns: SNS;
    constructor() {
        this.sns = new SNS({
            region: process.env.REGION
        })
    }

    async publishMessages(messageData, messageGroupId, topicArn, brand) {
        const messageAttributes = {
            source: {
                DataType: 'String',
                StringValue: brand,
            },
        };
        let response;
        try {
            if (Array.isArray(messageData)) {
                for (const message of messageData) {
                    response = await this.publishMessageToSNS(topicArn, message, messageGroupId, messageAttributes);
                }
            } else {
                response = await this.publishMessageToSNS(topicArn, messageData, messageGroupId, messageAttributes);
            }

            return response;
        } catch (err) {
            console.log('ERR', err);
            return "Error publishing messages";
        }
    }

    async publishMessageToSNS(topicArn, message, messageGroupId, messageAttributes) {
        try {
            const res = await this.sns.publish({
                TopicArn: topicArn,
                Message: JSON.stringify(message),
                MessageGroupId: messageGroupId,
                MessageAttributes: messageAttributes
            }).promise();

            console.log('SNS', res);
            console.log('Message published successfully');

            return res
        } catch (err) {
            console.log('ERR', err);
            throw err;
        }
    }
}
