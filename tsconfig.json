{"extends": "./tsconfig.paths.json", "compilerOptions": {"lib": ["ESNext"], "moduleResolution": "node", "noUnusedLocals": true, "noUnusedParameters": true, "removeComments": true, "sourceMap": true, "target": "ES2020", "outDir": "lib"}, "include": ["src/**/*.ts", "serverless.ts"], "exclude": ["node_modules/**/*", ".serverless/**/*", ".webpack/**/*", "_warmup/**/*", ".vscode/**/*"], "ts-node": {"require": ["tsconfig-paths/register"]}}