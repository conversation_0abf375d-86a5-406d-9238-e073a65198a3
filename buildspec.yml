version: 0.2
run-as: root

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - node --version
      - npm install -g yarn
      - yarn install
      - yarn global add serverless@3.38.0
      - yarn global add serverless-esbuild
  pre_build:
    commands:
      - git config --global credential.helper '!aws codecommit credential-helper $@'
      - git config --global credential.UseHttpPath true
      - echo "Fetching changes..."
      - git fetch --depth=2
      - echo "Detecting changes..."
      - |
        PREV_COMMIT=$(git rev-parse HEAD^1 || echo "")
        if [ -n "$PREV_COMMIT" ]; then
          CHANGED_FILES=$(git diff --name-only $PREV_COMMIT HEAD)
        else
          CHANGED_FILES=""
        fi
        echo "Changed files: $CHANGED_FILES"
        echo "$CHANGED_FILES" > changed_files.txt
      - cat changed_files.txt
  build:
    commands:
      - echo "Building and deploying based on changes..."
      - |
        if grep -qE '(src/connectors|src/libs|serverless.ts|buildspec.yml|package.json|tsconfig.json|tsconfig.path.json)' changed_files.txt; then
          echo "Common files changed, deploying whole stack"
          sls deploy --stage ${stage} --verbose
        else
          if grep -q 'src/functions/cognito-trigger' changed_files.txt; then
            echo "Deploying cognitoTrigger functionS"
            sls deploy function --function cognitoCustomEmailTrigger --stage ${stage} --verbose
            sls deploy function --function cognitoPreSignUpTrigger --stage ${stage} --verbose
            sls deploy function --function cognitoPreAuthenticationTrigger --stage ${stage} --verbose
            sls deploy function --function cognitoPostConfirmationTrigger --stage ${stage} --verbose
          else
            echo "No changes in cognitoTrigger functionS"
          fi
          if grep -q 'src/functions/custom-auth-service' changed_files.txt; then
            echo "Deploying customAuthorizer function"
            sls deploy function --function customAuthorizer --stage ${stage} --verbose
          else
            echo "No changes in customAuthorizer function"
          fi
          if grep -q 'src/functions/hzu-service' changed_files.txt; then
            echo "Deploying hzu service functions"
            # Deploy existing function
            sls deploy function --function hzuSfQueue --stage ${stage} --verbose
            sls deploy function --function hzuChangeRequestQueue --stage ${stage} --verbose
            # Try to deploy new function, fall back to full deployment if it doesn't exist
            if ! sls deploy function --function hzuChangeRequestQueue --stage ${stage} --verbose; then
              echo "hzuChangeRequestQueue function not yet deployed, performing full deployment"
              sls deploy --stage ${stage} --verbose
            fi
          else
            echo "No changes in hzu service functions"
          fi
          if grep -q 'src/functions/ibat-el-service' changed_files.txt; then
            echo "Deploying ibat-el service function"
            sls deploy function --function ibatSfQueue --stage ${stage} --verbose
          else
            echo "No changes in unfcSfQueue function"
          fi
          if grep -q 'src/functions/unfc-service' changed_files.txt; then
            echo "Deploying unfc service function"
            sls deploy function --function unfcSfQueue --stage ${stage} --verbose
          else
            echo "No changes in unfcSfQueue function"
          fi
          if grep -q 'src/functions/lim-service' changed_files.txt; then
            echo "Deploying lim service function"
            sls deploy function --function limSfQueue --stage ${stage} --verbose
          else
            echo "No changes in limSfQueue function"
                    fi
          if grep -q 'src/functions/ucw-legacy-apps' changed_files.txt; then
            echo "Deploying ucw legacy apps function"
            sls deploy function --function ucwLegacyAppHandler --stage ${stage} --verbose
          else
            echo "No changes in ucwLegacyAppHandler function"
          fi
          if grep -q 'src/functions/ueg-service' changed_files.txt; then
            echo "Deploying ueg service function"
            sls deploy function --function uegSfQueue --stage ${stage} --verbose
          else
            echo "No changes in uegSfQueue function"
          fi
          if grep -q 'src/functions/ucw-service' changed_files.txt; then
            echo "Deploying ucw service function"
            sls deploy function --function ucwSfQueue --stage ${stage} --verbose
          else
            echo "No changes in ucwSfQueue function"
          fi
          if grep -q 'src/functions/lsbfmyr-service' changed_files.txt; then
            echo "Deploying lsbfmyr service function"
            sls deploy function --function lsbfmyrSfQueue --stage ${stage} --verbose
          else
            echo "No changes in lsbfmyrSfQueue function"
          fi
          if grep -q 'src/functions/wul-service' changed_files.txt; then
            echo "Deploying wulSfQueue function"
            sls deploy function --function wulSfQueue --stage ${stage} --verbose
          else
            echo "No changes in wulSfQueue function"
          fi
          if grep -q 'src/functions/ard-service' changed_files.txt; then
            echo "Deploying ardSfQueue function"
            sls deploy function --function ardSfQueue --stage ${stage} --verbose
          else
            echo "No changes in ardSfQueue function"
          fi
          if grep -q 'src/functions/notification-service' changed_files.txt; then
            echo "Deploying notification service function"
            sls deploy function --function oapStudentNotification --stage ${stage} --verbose
          else
            echo "No changes in oapStudentNotification function"
          fi
          if grep -q 'src/functions/oap-logs-exporter-service' changed_files.txt; then
            echo "Deploying oapLogsExporter service function"
            sls deploy function --function oapLogsExporter --stage ${stage} --verbose
          else
            echo "No changes in oapLogsExporter function"
          fi
          if grep -q 'src/functions/sqs-lambda' changed_files.txt; then
            echo "Deploying sqs lambda service function"
            sls deploy function --function sqsLambdaTrigger --stage ${stage} --verbose
          else
            echo "No changes in sqsLambdaTrigger function"
          fi
          if grep -q 'src/functions/student-service' changed_files.txt; then
            echo "Deploying student service function"
            sls deploy function --function studentLeadOwnerAssignment --stage ${stage} --verbose
          else
            echo "No changes in studentLeadOwnerAssignment function"
          fi
          if grep -q 'src/functions/s3MetadataModifier' changed_files.txt; then
            echo "Deploying student service function"
            sls deploy function --function s3MetadataModifier --stage ${stage} --verbose
          else
            echo "No changes in s3MetadataModifier function"
          fi
          # Add additional services as needed
        fi
cache:
  paths:
    - node_modules
